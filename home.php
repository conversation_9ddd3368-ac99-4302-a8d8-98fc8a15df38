<?php
/**
 * BILLVAT - Multilingual Landing Page
 * AI-powered Invoice Processing System
 */

session_start();

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    $role = $_SESSION['user_role'];
    
    if (in_array($role, ['admin', 'editor'])) {
        header('Location: dashboard.php');
    } else {
        header('Location: kullanici_sayfasi.php');
    }
    exit;
}

// Language detection
$lang = $_GET['lang'] ?? $_SESSION['lang'] ?? 'tr';
$supported_langs = ['tr', 'en', 'fr', 'nl'];

if (!in_array($lang, $supported_langs)) {
    $lang = 'tr';
}

$_SESSION['lang'] = $lang;

// Load language file
$lang_file = "lang/{$lang}.json";
$translations = [];

if (file_exists($lang_file)) {
    $translations = json_decode(file_get_contents($lang_file), true);
}

function t($key, $default = '') {
    global $translations;
    return $translations[$key] ?? $default;
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('site_title', 'BILLVAT - AI Invoice Processing'); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo t('site_description', 'AI-powered invoice processing system with OCR and automatic categorization'); ?>">
    <meta name="keywords" content="<?php echo t('site_keywords', 'invoice, AI, OCR, automation, accounting'); ?>">
    <meta name="author" content="BILLVAT">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo t('site_title'); ?>">
    <meta property="og:description" content="<?php echo t('site_description'); ?>">
    <meta property="og:image" content="https://billvat.com/assets/images/og-image.jpg">
    <meta property="og:url" content="https://billvat.com">
    <meta property="og:type" content="website">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="BILLVAT">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/favicon-16x16.png">
    <link rel="apple-touch-icon" href="assets/icons/apple-touch-icon.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .hero-section {
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }
        
        .feature-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 20px;
            overflow: hidden;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
        }
        
        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .stats-section {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-elements::before {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-elements::after {
            bottom: 10%;
            right: 10%;
            animation-delay: 3s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .demo-image {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 100%;
            height: auto;
        }

        /* Home Page Responsive Styles */
        @media (max-width: 767px) {
            .hero-section {
                min-height: 80vh;
                padding: 2rem 0;
            }

            .hero-section .container {
                padding: 1rem;
            }

            .display-3 {
                font-size: 2rem;
            }

            .h3 {
                font-size: 1.25rem;
            }

            .lead {
                font-size: 1rem;
            }

            .btn-gradient {
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .btn-outline-light {
                padding: 10px 18px;
                font-size: 0.9rem;
            }

            .row.text-center .col-4 h4 {
                font-size: 1.25rem;
            }

            .row.text-center .col-4 small {
                font-size: 0.75rem;
            }

            .demo-image {
                margin-top: 2rem;
                border-radius: 15px;
            }

            .feature-card {
                margin-bottom: 1.5rem;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .stats-section .col-md-3 {
                margin-bottom: 2rem;
            }

            .stats-section h3 {
                font-size: 1.5rem;
            }

            .stats-section i {
                font-size: 2rem !important;
            }

            .language-switcher {
                top: 10px;
                right: 10px;
            }

            .language-switcher .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 575px) {
            .hero-section {
                min-height: 70vh;
                padding: 1rem 0;
            }

            .hero-section .container {
                padding: 0.5rem;
            }

            .display-3 {
                font-size: 1.75rem;
            }

            .h3 {
                font-size: 1.1rem;
            }

            .lead {
                font-size: 0.9rem;
            }

            .btn-gradient,
            .btn-outline-light {
                width: 100%;
                margin-bottom: 0.5rem;
                padding: 10px 15px;
                font-size: 0.85rem;
            }

            .d-flex.flex-wrap.gap-3 {
                flex-direction: column;
                gap: 0.5rem !important;
            }

            .row.text-center {
                margin-top: 1.5rem;
            }

            .row.text-center .col-4 h4 {
                font-size: 1.1rem;
            }

            .row.text-center .col-4 small {
                font-size: 0.7rem;
            }

            .feature-icon {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .card-title {
                font-size: 1rem;
            }

            .card-text {
                font-size: 0.85rem;
            }

            .stats-section h3 {
                font-size: 1.25rem;
            }

            .stats-section p {
                font-size: 0.8rem;
            }

            .language-switcher {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 1rem;
                text-align: center;
            }

            .navbar-brand {
                font-size: 1rem;
            }

            .footer .row {
                text-align: center;
            }

            .footer .col-md-2,
            .footer .col-md-4 {
                margin-bottom: 2rem;
            }
        }

        /* Landscape phone adjustments */
        @media (max-width: 767px) and (orientation: landscape) {
            .hero-section {
                min-height: 100vh;
            }

            .hero-section .row {
                align-items: center;
            }

            .demo-image {
                max-height: 300px;
                object-fit: cover;
            }
        }

        /* Tablet adjustments */
        @media (min-width: 768px) and (max-width: 991px) {
            .display-3 {
                font-size: 2.5rem;
            }

            .btn-gradient {
                padding: 14px 25px;
            }

            .feature-icon {
                width: 70px;
                height: 70px;
                font-size: 1.75rem;
            }
        }

        /* Large screen enhancements */
        @media (min-width: 1200px) {
            .hero-section {
                min-height: 100vh;
            }

            .display-3 {
                font-size: 3.5rem;
            }

            .feature-card:hover {
                transform: translateY(-15px);
                box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            }

            .btn-gradient:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            }
        }

        /* Touch-friendly adjustments */
        @media (hover: none) and (pointer: coarse) {
            .btn-gradient,
            .btn-outline-light {
                min-height: 44px;
                min-width: 44px;
            }

            .language-switcher .btn {
                min-height: 44px;
            }

            .feature-card {
                cursor: pointer;
            }

            .feature-card:active {
                transform: scale(0.98);
            }
        }

        /* Print styles */
        @media print {
            .language-switcher,
            .btn,
            .floating-elements {
                display: none !important;
            }

            .hero-section {
                background: none !important;
                color: #000 !important;
                min-height: auto;
            }

            .feature-card,
            .stats-section {
                break-inside: avoid;
            }
        }
    </style>

    <!-- Responsive CSS -->
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>

<!-- Language Switcher -->
<div class="language-switcher">
    <div class="dropdown">
        <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-globe me-2"></i>
            <?php 
            $lang_names = ['tr' => 'Türkçe', 'en' => 'English', 'fr' => 'Français', 'nl' => 'Nederlands'];
            echo $lang_names[$lang];
            ?>
        </button>
        <ul class="dropdown-menu">
            <?php foreach ($supported_langs as $l): ?>
                <li><a class="dropdown-item <?php echo $l === $lang ? 'active' : ''; ?>" href="?lang=<?php echo $l; ?>">
                    <?php echo $lang_names[$l]; ?>
                </a></li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>

<!-- Hero Section -->
<section class="hero-section text-white">
    <div class="floating-elements"></div>
    <div class="container position-relative">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="display-3 fw-bold mb-4">
                    <i class="fas fa-robot me-3"></i>
                    BILLVAT
                </h1>
                <h2 class="h3 mb-4"><?php echo t('hero_subtitle', 'AI-Powered Invoice Processing'); ?></h2>
                <p class="lead mb-4">
                    <?php echo t('hero_description', 'Transform your invoice processing with artificial intelligence. Automatic OCR, smart categorization, and seamless integration.'); ?>
                </p>
                <div class="d-flex flex-wrap gap-3 mb-4">
                    <a href="login.php" class="btn-gradient">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <?php echo t('login_button', 'Login'); ?>
                    </a>
                    <a href="#demo" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-play me-2"></i>
                        <?php echo t('demo_button', 'Watch Demo'); ?>
                    </a>
                </div>
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="fw-bold">99%</h4>
                        <small><?php echo t('accuracy_label', 'Accuracy'); ?></small>
                    </div>
                    <div class="col-4">
                        <h4 class="fw-bold">2s</h4>
                        <small><?php echo t('processing_time', 'Processing'); ?></small>
                    </div>
                    <div class="col-4">
                        <h4 class="fw-bold">24/7</h4>
                        <small><?php echo t('availability', 'Available'); ?></small>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 text-center" data-aos="fade-left">
                <img src="assets/images/dashboard-preview.png" alt="BILLVAT Dashboard" class="demo-image">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5" id="features">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h2 class="display-5 fw-bold text-primary"><?php echo t('features_title', 'Features'); ?></h2>
                <p class="lead text-muted"><?php echo t('features_subtitle', 'Powerful tools for modern businesses'); ?></p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon" style="background: var(--primary-gradient);">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h5 class="card-title"><?php echo t('feature_ocr_title', 'AI-Powered OCR'); ?></h5>
                        <p class="card-text">
                            <?php echo t('feature_ocr_desc', 'Advanced optical character recognition with 99% accuracy for multiple languages and document types.'); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon" style="background: var(--secondary-gradient);">
                            <i class="fas fa-tags"></i>
                        </div>
                        <h5 class="card-title"><?php echo t('feature_categorization_title', 'Smart Categorization'); ?></h5>
                        <p class="card-text">
                            <?php echo t('feature_categorization_desc', 'Automatic invoice categorization with 22 predefined categories and custom rules.'); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon" style="background: var(--success-gradient);">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <h5 class="card-title"><?php echo t('feature_cloud_title', 'Cloud Storage'); ?></h5>
                        <p class="card-text">
                            <?php echo t('feature_cloud_desc', 'S3-compatible cloud storage with automatic backup and secure file management.'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4" data-aos="zoom-in" data-aos-delay="100">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-file-invoice fa-3x text-primary mb-3"></i>
                        <h3 class="fw-bold">50,000+</h3>
                        <p class="text-muted"><?php echo t('stat_invoices', 'Invoices Processed'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4" data-aos="zoom-in" data-aos-delay="200">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-success mb-3"></i>
                        <h3 class="fw-bold">1,000+</h3>
                        <p class="text-muted"><?php echo t('stat_users', 'Active Users'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4" data-aos="zoom-in" data-aos-delay="300">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-clock fa-3x text-info mb-3"></i>
                        <h3 class="fw-bold">95%</h3>
                        <p class="text-muted"><?php echo t('stat_time_saved', 'Time Saved'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4" data-aos="zoom-in" data-aos-delay="400">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                        <h3 class="fw-bold">100%</h3>
                        <p class="text-muted"><?php echo t('stat_security', 'Secure'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Demo Section -->
<section class="py-5" id="demo">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h2 class="display-6 fw-bold mb-4"><?php echo t('demo_title', 'See It In Action'); ?></h2>
                <p class="lead mb-4">
                    <?php echo t('demo_description', 'Watch how BILLVAT processes invoices in real-time with AI-powered accuracy.'); ?>
                </p>
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> <?php echo t('demo_feature_1', 'Upload any invoice format'); ?></li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> <?php echo t('demo_feature_2', 'Automatic data extraction'); ?></li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> <?php echo t('demo_feature_3', 'Smart categorization'); ?></li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i> <?php echo t('demo_feature_4', 'Export to accounting systems'); ?></li>
                </ul>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="position-relative">
                    <img src="assets/images/invoice-processing.png" alt="Invoice Processing Demo" class="demo-image">
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <button class="btn btn-light btn-lg rounded-circle" style="width: 80px; height: 80px;">
                            <i class="fas fa-play text-primary fa-2x"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5" style="background: var(--primary-gradient);">
    <div class="container text-center text-white">
        <div class="row">
            <div class="col-lg-8 mx-auto" data-aos="fade-up">
                <h2 class="display-6 fw-bold mb-4"><?php echo t('cta_title', 'Ready to Transform Your Business?'); ?></h2>
                <p class="lead mb-4">
                    <?php echo t('cta_description', 'Join thousands of businesses already using BILLVAT to streamline their invoice processing.'); ?>
                </p>
                <a href="login.php" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-rocket me-2"></i>
                    <?php echo t('cta_button', 'Get Started Now'); ?>
                </a>
                <a href="#contact" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-envelope me-2"></i>
                    <?php echo t('contact_button', 'Contact Us'); ?>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4">
                <h5 class="fw-bold mb-3">BILLVAT</h5>
                <p><?php echo t('footer_description', 'AI-powered invoice processing for modern businesses.'); ?></p>
                <div class="d-flex gap-3">
                    <a href="#" class="text-white"><i class="fab fa-twitter fa-lg"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-linkedin fa-lg"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-github fa-lg"></i></a>
                </div>
            </div>
            <div class="col-md-2 mb-4">
                <h6 class="fw-bold mb-3"><?php echo t('footer_product', 'Product'); ?></h6>
                <ul class="list-unstyled">
                    <li><a href="#features" class="text-white-50"><?php echo t('footer_features', 'Features'); ?></a></li>
                    <li><a href="#demo" class="text-white-50"><?php echo t('footer_demo', 'Demo'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_pricing', 'Pricing'); ?></a></li>
                </ul>
            </div>
            <div class="col-md-2 mb-4">
                <h6 class="fw-bold mb-3"><?php echo t('footer_company', 'Company'); ?></h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-white-50"><?php echo t('footer_about', 'About'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_careers', 'Careers'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_contact', 'Contact'); ?></a></li>
                </ul>
            </div>
            <div class="col-md-2 mb-4">
                <h6 class="fw-bold mb-3"><?php echo t('footer_support', 'Support'); ?></h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-white-50"><?php echo t('footer_help', 'Help Center'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_docs', 'Documentation'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_api', 'API'); ?></a></li>
                </ul>
            </div>
            <div class="col-md-2 mb-4">
                <h6 class="fw-bold mb-3"><?php echo t('footer_legal', 'Legal'); ?></h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-white-50"><?php echo t('footer_privacy', 'Privacy'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_terms', 'Terms'); ?></a></li>
                    <li><a href="#" class="text-white-50"><?php echo t('footer_security', 'Security'); ?></a></li>
                </ul>
            </div>
        </div>
        <hr class="my-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0">&copy; 2024 BILLVAT. <?php echo t('footer_rights', 'All rights reserved.'); ?></p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0"><?php echo t('footer_made_with', 'Made with'); ?> <i class="fas fa-heart text-danger"></i> <?php echo t('footer_location', 'in Turkey'); ?></p>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- AOS Animation -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- PWA Service Worker -->
<script>
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed: ', err);
            });
    });
}

// Initialize AOS
AOS.init({
    duration: 1000,
    once: true
});
</script>

</body>
</html>
