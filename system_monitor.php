<?php
/**
 * BILLVAT System Monitor
 * Sistem izleme dashboard'u
 */

session_start();

// Admin kontrolü
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'includes/cache_manager.php';
require_once 'includes/logger.php';
require_once 'includes/performance_monitor.php';
require_once 'includes/rate_limiter.php';
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-gradient mb-1">
                        <i class="fas fa-chart-line me-2"></i>
                        Sistem İzleme
                    </h2>
                    <p class="text-muted mb-0">Gerçek zamanlı sistem performansı ve logları</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="refreshAll()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Yenile
                    </button>
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-trash me-2"></i>
                        Cache Temizle
                    </button>
                </div>
            </div>

            <!-- System Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-server fa-2x mb-2"></i>
                            <h4 id="cpu-usage">-</h4>
                            <p class="mb-0">CPU Kullanımı</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-memory fa-2x mb-2"></i>
                            <h4 id="memory-usage">-</h4>
                            <p class="mb-0">Bellek Kullanımı</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-hdd fa-2x mb-2"></i>
                            <h4 id="disk-usage">-</h4>
                            <p class="mb-0">Disk Kullanımı</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h4 id="avg-response">-</h4>
                            <p class="mb-0">Ort. Yanıt Süresi</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monitoring Tabs -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="monitorTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                                <i class="fas fa-tachometer-alt me-2"></i>Performans
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                                <i class="fas fa-file-alt me-2"></i>Loglar
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="cache-tab" data-bs-toggle="tab" data-bs-target="#cache" type="button" role="tab">
                                <i class="fas fa-database me-2"></i>Cache
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="rate-limit-tab" data-bs-toggle="tab" data-bs-target="#rate-limit" type="button" role="tab">
                                <i class="fas fa-shield-alt me-2"></i>Rate Limiting
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="monitorTabContent">
                        <!-- Performans Tab -->
                        <div class="tab-pane fade show active" id="performance" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <canvas id="responseTimeChart" width="400" height="200"></canvas>
                                </div>
                                <div class="col-md-6">
                                    <canvas id="memoryUsageChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6>Son Performans Metrikleri</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm" id="performance-table">
                                            <thead>
                                                <tr>
                                                    <th>Zaman</th>
                                                    <th>Action</th>
                                                    <th>Süre (ms)</th>
                                                    <th>Bellek</th>
                                                    <th>Kullanıcı</th>
                                                    <th>Sorunlar</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Loglar Tab -->
                        <div class="tab-pane fade" id="logs" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <select class="form-select" id="log-level">
                                        <option value="">Tüm Seviyeler</option>
                                        <option value="error">Error</option>
                                        <option value="warning">Warning</option>
                                        <option value="info">Info</option>
                                        <option value="debug">Debug</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <input type="date" class="form-control" id="log-date" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary" onclick="loadLogs()">
                                        <i class="fas fa-search me-2"></i>Filtrele
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-sm" id="logs-table">
                                    <thead>
                                        <tr>
                                            <th>Zaman</th>
                                            <th>Seviye</th>
                                            <th>Mesaj</th>
                                            <th>Kullanıcı</th>
                                            <th>IP</th>
                                            <th>Detaylar</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Cache Tab -->
                        <div class="tab-pane fade" id="cache" role="tabpanel">
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h4 id="cache-files">-</h4>
                                            <p class="mb-0">Cache Dosyaları</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h4 id="cache-size">-</h4>
                                            <p class="mb-0">Cache Boyutu</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h4 id="cache-hit-rate">-</h4>
                                            <p class="mb-0">Hit Rate</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button class="btn btn-warning" onclick="clearExpiredCache()">
                                        <i class="fas fa-broom me-2"></i>
                                        Süresi Dolmuş Cache'leri Temizle
                                    </button>
                                    <button class="btn btn-danger ms-2" onclick="clearAllCache()">
                                        <i class="fas fa-trash me-2"></i>
                                        Tüm Cache'i Temizle
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Rate Limiting Tab -->
                        <div class="tab-pane fade" id="rate-limit" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table" id="rate-limit-table">
                                    <thead>
                                        <tr>
                                            <th>Identifier</th>
                                            <th>Action</th>
                                            <th>İstek Sayısı</th>
                                            <th>Limit</th>
                                            <th>Sıfırlanma</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    loadSystemStats();
    loadPerformanceData();
    loadLogs();
    loadCacheStats();
    loadRateLimitStats();
    
    // Auto refresh every 30 seconds
    setInterval(function() {
        loadSystemStats();
        loadPerformanceData();
    }, 30000);
});

function loadSystemStats() {
    $.get('api.php?action=getSystemStats', function(response) {
        if (response.success) {
            const stats = response.stats;
            $('#cpu-usage').text(stats.cpu + '%');
            $('#memory-usage').text(stats.memory + '%');
            $('#disk-usage').text(stats.disk + '%');
            $('#avg-response').text(stats.avg_response + 'ms');
        }
    });
}

function loadPerformanceData() {
    $.get('api.php?action=getPerformanceData', function(response) {
        if (response.success) {
            updatePerformanceCharts(response.data);
            updatePerformanceTable(response.recent);
        }
    });
}

function loadLogs() {
    const level = $('#log-level').val();
    const date = $('#log-date').val();
    
    $.get('api.php?action=getSystemLogs', {level: level, date: date}, function(response) {
        if (response.success) {
            updateLogsTable(response.logs);
        }
    });
}

function loadCacheStats() {
    $.get('api.php?action=getCacheStats', function(response) {
        if (response.success) {
            const stats = response.stats;
            $('#cache-files').text(stats.total_files);
            $('#cache-size').text(stats.total_size_mb + ' MB');
            $('#cache-hit-rate').text(stats.hit_rate + '%');
        }
    });
}

function loadRateLimitStats() {
    $.get('api.php?action=getRateLimitStats', function(response) {
        if (response.success) {
            updateRateLimitTable(response.stats);
        }
    });
}

function refreshAll() {
    loadSystemStats();
    loadPerformanceData();
    loadLogs();
    loadCacheStats();
    loadRateLimitStats();
    showAlert('Veriler yenilendi', 'success');
}

function clearCache() {
    if (confirm('Tüm cache temizlensin mi?')) {
        $.post('api.php?action=clearCache', function(response) {
            if (response.success) {
                showAlert('Cache temizlendi', 'success');
                loadCacheStats();
            } else {
                showAlert('Hata: ' + response.message, 'danger');
            }
        });
    }
}

function clearExpiredCache() {
    $.post('api.php?action=clearExpiredCache', function(response) {
        if (response.success) {
            showAlert(response.deleted + ' dosya temizlendi', 'success');
            loadCacheStats();
        }
    });
}

function clearAllCache() {
    if (confirm('TÜM cache silinsin mi? Bu işlem geri alınamaz!')) {
        $.post('api.php?action=clearAllCache', function(response) {
            if (response.success) {
                showAlert('Tüm cache temizlendi', 'success');
                loadCacheStats();
            }
        });
    }
}

// Chart ve tablo güncelleme fonksiyonları burada olacak...
</script>

<?php require_once 'includes/footer.php'; ?>
