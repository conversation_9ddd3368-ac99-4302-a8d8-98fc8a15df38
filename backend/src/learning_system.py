"""
Ölçeklenebilir Öğrenme Sistemi
- SQLite veritabanı kullanır
- Hızlı arama ve indeksleme
- Büyük veri setleri için optimize edilmiş
- Otomatik temizlik ve bakım
"""

import sqlite3
import json
import hashlib
import os
from datetime import datetime
from pathlib import Path
import logging

class LearningSystem:
    def __init__(self, db_path="veri/learning.db"):
        self.db_path = db_path
        self.ensure_db_directory()
        self.init_database()
        
    def ensure_db_directory(self):
        """Veritabanı klasörünü oluştur"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
    def init_database(self):
        """Veritabanını başlat ve tabloları oluştur"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS learning_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    hash TEXT UNIQUE NOT NULL,
                    text_content TEXT NOT NULL,
                    correction_json TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    usage_count INTEGER DEFAULT 1,
                    confidence REAL DEFAULT 0.0,
                    source TEXT DEFAULT 'manual',
                    user_id TEXT,
                    filename TEXT,
                    category TEXT,
                    document_type TEXT
                )
            ''')
            
            # İndeksler oluştur
            conn.execute('CREATE INDEX IF NOT EXISTS idx_hash ON learning_data(hash)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_category ON learning_data(category)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_document_type ON learning_data(document_type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON learning_data(created_at)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_usage_count ON learning_data(usage_count)')
            
            conn.commit()
            
    def add_learning_data(self, original_data, corrected_data, user_id=None, filename=None, source='manual'):
        """Sadece değişiklik varsa öğrenme verisi ekle"""
        try:
            # Değişiklik kontrolü
            if not self._has_meaningful_changes(original_data, corrected_data):
                logging.info(f"Anlamlı değişiklik yok, öğrenme verisi eklenmedi: {filename}")
                return False

            # Düzeltme metnini oluştur
            correction_text = self._create_correction_text(original_data, corrected_data, filename)

            # Hash oluştur (düzeltme metninden)
            text_hash = hashlib.md5(correction_text.encode('utf-8')).hexdigest()

            # Kategori ve belge türü
            category = corrected_data.get('kategori', 'UNDEFINED')
            document_type = corrected_data.get('belge_turu', 'undefined')
            confidence = float(corrected_data.get('confidence', 0.0))

            # Kategoriye göre dosya yolu
            category_db_path = self._get_category_db_path(category)

            with sqlite3.connect(category_db_path) as conn:
                # Tabloyu oluştur (yoksa)
                self._ensure_category_table(conn)

                # Mevcut kayıt var mı kontrol et
                existing = conn.execute(
                    'SELECT id, usage_count FROM learning_data WHERE hash = ?',
                    (text_hash,)
                ).fetchone()

                if existing:
                    # Mevcut kaydı güncelle
                    conn.execute('''
                        UPDATE learning_data
                        SET usage_count = usage_count + 1,
                            updated_at = CURRENT_TIMESTAMP,
                            confidence = MAX(confidence, ?)
                        WHERE hash = ?
                    ''', (confidence, text_hash))
                    logging.info(f"Mevcut öğrenme verisi güncellendi: {text_hash}")
                else:
                    # Yeni kayıt ekle
                    conn.execute('''
                        INSERT INTO learning_data
                        (hash, correction_text, original_json, corrected_json, confidence, source, user_id, filename, category, document_type)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (text_hash, correction_text,
                          json.dumps(original_data, ensure_ascii=False),
                          json.dumps(corrected_data, ensure_ascii=False),
                          confidence, source, user_id, filename, category, document_type))
                    logging.info(f"Yeni öğrenme verisi eklendi: {text_hash} (kategori: {category})")

                conn.commit()
                return True

        except Exception as e:
            logging.error(f"Öğrenme verisi eklenirken hata: {e}")
            return False

    def _has_meaningful_changes(self, original, corrected):
        """Anlamlı değişiklik olup olmadığını kontrol et"""
        # Önemli alanlar
        important_fields = [
            'belge_turu', 'fatura_numarasi', 'fatura_tutari', 'kdv_tutari',
            'kdv_orani', 'icerik', 'kategori', 'firma_adi', 'tarih', 'parabirimi'
        ]

        changes_count = 0
        for field in important_fields:
            original_val = str(original.get(field, '')).strip()
            corrected_val = str(corrected.get(field, '')).strip()

            if original_val != corrected_val:
                changes_count += 1

        # En az 2 önemli alanda değişiklik olmalı veya belge türü değişmeli
        return changes_count >= 2 or original.get('belge_turu') != corrected.get('belge_turu')

    def _create_correction_text(self, original, corrected, filename):
        """Düzeltme metnini oluştur"""
        changes = []

        # Değişiklikleri tespit et
        for key, new_value in corrected.items():
            old_value = original.get(key, '')
            if str(old_value).strip() != str(new_value).strip():
                changes.append(f"{key}: '{old_value}' -> '{new_value}'")

        return f"Dosya: {filename} | Değişiklikler: {' | '.join(changes)}"

    def _get_category_db_path(self, category):
        """Kategoriye göre veritabanı yolu"""
        # Güvenli dosya adı oluştur
        safe_category = "".join(c for c in category if c.isalnum() or c in ('-', '_')).upper()
        if not safe_category:
            safe_category = "UNDEFINED"

        db_dir = Path(self.db_path).parent / "learning_categories"
        db_dir.mkdir(exist_ok=True)

        return str(db_dir / f"learning_{safe_category}.db")

    def _ensure_category_table(self, conn):
        """Kategori tablosunu oluştur"""
        conn.execute('''
            CREATE TABLE IF NOT EXISTS learning_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hash TEXT UNIQUE NOT NULL,
                correction_text TEXT NOT NULL,
                original_json TEXT NOT NULL,
                corrected_json TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                usage_count INTEGER DEFAULT 1,
                confidence REAL DEFAULT 0.0,
                source TEXT DEFAULT 'manual',
                user_id TEXT,
                filename TEXT,
                category TEXT,
                document_type TEXT
            )
        ''')

        # İndeksler
        conn.execute('CREATE INDEX IF NOT EXISTS idx_hash ON learning_data(hash)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_category ON learning_data(category)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_document_type ON learning_data(document_type)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_usage_count ON learning_data(usage_count)')

    def search_similar(self, text_content, limit=5):
        """Benzer öğrenme verilerini ara"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # En çok kullanılan ve yüksek güvenli verileri getir
                results = conn.execute('''
                    SELECT text_content, correction_json, usage_count, confidence, category, document_type
                    FROM learning_data 
                    WHERE confidence > 0.7
                    ORDER BY usage_count DESC, confidence DESC
                    LIMIT ?
                ''', (limit,)).fetchall()
                
                return [
                    {
                        'text': row[0],
                        'correction': json.loads(row[1]),
                        'usage_count': row[2],
                        'confidence': row[3],
                        'category': row[4],
                        'document_type': row[5]
                    }
                    for row in results
                ]
                
        except Exception as e:
            logging.error(f"Benzer veri aranırken hata: {e}")
            return []
            
    def get_statistics(self):
        """Öğrenme sistemi istatistikleri (tüm kategorilerden)"""
        try:
            stats = {
                'total_records': 0,
                'categories': {},
                'document_types': {},
                'average_confidence': 0,
                'top_used': [],
                'category_files': []
            }

            # Kategori klasörünü kontrol et
            db_dir = Path(self.db_path).parent / "learning_categories"
            if not db_dir.exists():
                return stats

            all_records = []

            # Her kategori dosyasını oku
            for db_file in db_dir.glob("learning_*.db"):
                category_name = db_file.stem.replace('learning_', '')

                try:
                    with sqlite3.connect(str(db_file)) as conn:
                        # Kategori kayıt sayısı
                        count = conn.execute('SELECT COUNT(*) FROM learning_data').fetchone()[0]
                        if count > 0:
                            stats['categories'][category_name] = count
                            stats['total_records'] += count
                            stats['category_files'].append({
                                'category': category_name,
                                'file': str(db_file),
                                'count': count,
                                'size': db_file.stat().st_size
                            })

                        # Belge türleri
                        doc_types = conn.execute('''
                            SELECT document_type, COUNT(*) as count
                            FROM learning_data
                            WHERE document_type != ''
                            GROUP BY document_type
                        ''').fetchall()

                        for doc_type, count in doc_types:
                            stats['document_types'][doc_type] = stats['document_types'].get(doc_type, 0) + count

                        # Tüm kayıtları topla (istatistik için)
                        records = conn.execute('''
                            SELECT filename, usage_count, confidence
                            FROM learning_data
                        ''').fetchall()
                        all_records.extend(records)

                except Exception as e:
                    logging.error(f"Kategori dosyası okunamadı {db_file}: {e}")

            # Ortalama güven skoru
            if all_records:
                total_confidence = sum(record[2] for record in all_records)
                stats['average_confidence'] = round(total_confidence / len(all_records), 2)

                # En çok kullanılan veriler
                sorted_records = sorted(all_records, key=lambda x: x[1], reverse=True)[:10]
                stats['top_used'] = [
                    {'filename': record[0], 'usage_count': record[1], 'confidence': record[2]}
                    for record in sorted_records
                ]

            return stats

        except Exception as e:
            logging.error(f"İstatistik alınırken hata: {e}")
            return {}
            
    def cleanup_old_data(self, days=365):
        """Eski ve az kullanılan verileri temizle"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 1 yıldan eski ve 2'den az kullanılan verileri sil
                deleted = conn.execute('''
                    DELETE FROM learning_data 
                    WHERE created_at < datetime('now', '-{} days')
                    AND usage_count < 2
                    AND confidence < 0.5
                '''.format(days)).rowcount
                
                conn.commit()
                logging.info(f"Temizlik: {deleted} eski kayıt silindi")
                return deleted
                
        except Exception as e:
            logging.error(f"Temizlik sırasında hata: {e}")
            return 0
            
    def migrate_from_json(self, json_file_path):
        """Eski JSON dosyasından verileri taşı"""
        try:
            if not os.path.exists(json_file_path):
                return False
                
            with open(json_file_path, 'r', encoding='utf-8') as f:
                old_data = json.load(f)
                
            migrated_count = 0
            for item in old_data:
                if 'metin' in item and 'duzeltme' in item:
                    # Eski format
                    text_content = item['metin']
                    correction_json = item['duzeltme']
                elif 'text' in item and 'result' in item:
                    # Yeni format
                    text_content = item['text']
                    correction_json = item['result']
                else:
                    continue
                    
                if self.add_learning_data(
                    text_content=text_content,
                    correction_json=correction_json,
                    user_id=item.get('user', 'migrated'),
                    filename=item.get('filename', 'migrated'),
                    source='migration'
                ):
                    migrated_count += 1
                    
            logging.info(f"Migration tamamlandı: {migrated_count} kayıt taşındı")
            return migrated_count
            
        except Exception as e:
            logging.error(f"Migration sırasında hata: {e}")
            return 0

# Global instance
learning_system = LearningSystem()

def get_learning_system():
    """Global öğrenme sistemi instance'ını döndür"""
    return learning_system
