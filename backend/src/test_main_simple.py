#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basit Ana Test - Google Vision API olmadan
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Basit OCR simülasyonu
def simple_ocr(file_path):
    """Dosyadan metin okur veya demo metin döner"""
    try:
        if file_path.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            # Demo fatura metni
            return """
            ÖRNEK FATURA
            
            ABC Teknoloji A.Ş.
            Adres: İstanbul, Türkiye
            Tel: 0212 123 45 67
            
            Fatura No: F-2024-001
            Tarih: 15.01.2024
            
            Ürün/Hizmet: Yazılım Geliştirme Hizmeti
            Miktar: 1
            Birim Fiyat: 1000.00 TL
            
            Ara Toplam: 1000.00 TL
            KDV (%18): 180.00 TL
            TOPLAM: 1180.00 TL
            
            Teşekkürler.
            """
    except Exception as e:
        print(f"OCR hatası: {e}")
        return ""

# Basit GPT simülasyonu
def simple_gpt_analysis(text, config):
    """Basit GPT analizi simülasyonu"""
    try:
        # Metinden tutar çıkarmaya çalış
        lines = text.split('\n')
        fatura_tutari = ""
        kdv_tutari = ""
        
        for line in lines:
            if 'TOPLAM:' in line.upper():
                parts = line.split(':')
                if len(parts) > 1:
                    fatura_tutari = parts[1].strip().replace('TL', '').strip()
            elif 'KDV' in line.upper():
                parts = line.split(':')
                if len(parts) > 1:
                    kdv_tutari = parts[1].strip().replace('TL', '').strip()
        
        # JSON sonucu oluştur
        result = {
            "fatura_tutari": fatura_tutari or "1180.00",
            "kdv_tutari": kdv_tutari or "180.00",
            "icerik": "gider",
            "kategori": config.get('kategoriler', ['teknoloji'])[0],
            "firma_adi": config.get('firma_adi', 'ABC Teknoloji A.Ş.'),
            "tarih": "2024-01-15",
            "confidence": "0.85"
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        print(f"GPT analizi hatası: {e}")
        return json.dumps({
            "fatura_tutari": "0.00",
            "kdv_tutari": "0.00",
            "icerik": "gider",
            "kategori": "diğer",
            "firma_adi": "BILINMIYOR",
            "tarih": "",
            "confidence": "0.0"
        }, ensure_ascii=False, indent=2)

def main():
    """Ana test fonksiyonu"""
    print("🚀 BILLVAT SİSTEMİ - BASİT TEST")
    print("=" * 50)
    
    try:
        # Konfigürasyonu yükle
        print("📋 Konfigürasyon yükleniyor...")
        with open('../config/global_config.json', 'r', encoding='utf-8') as f:
            global_config = json.load(f)
        
        with open('../../veri/kullanicilar/kisi1/config.json', 'r', encoding='utf-8') as f:
            user_config = json.load(f)
        
        config = {**global_config, **user_config}
        print("✅ Konfigürasyon yüklendi")
        
        # Test dosyalarını bul
        test_dir = Path("../../veri/kullanicilar/kisi1")
        test_files = []
        
        # Desteklenen uzantılar
        extensions = ['.txt', '.jpg', '.jpeg', '.png', '.pdf']
        
        for ext in extensions:
            test_files.extend(list(test_dir.glob(f"*{ext}")))
            test_files.extend(list(test_dir.glob(f"*{ext.upper()}")))
        
        if not test_files:
            print("❌ Test dosyası bulunamadı!")
            return
        
        print(f"📁 {len(test_files)} dosya bulundu")
        
        # Çıktı klasörünü hazırla
        output_dir = Path("../../veri/duzeltme")
        output_dir.mkdir(exist_ok=True)
        
        # Dosyaları işle
        processed = 0
        for file_path in test_files:
            if file_path.name == 'config.json' or file_path.name == 'README.txt':
                continue
                
            print(f"\n🔍 İşleniyor: {file_path.name}")
            
            # OCR simülasyonu
            print("  📝 OCR işlemi...")
            text = simple_ocr(str(file_path))
            
            if not text.strip():
                print("  ❌ Metin çıkarılamadı")
                continue
            
            print(f"  ✅ {len(text)} karakter çıkarıldı")
            
            # GPT analizi simülasyonu
            print("  🤖 GPT analizi...")
            json_result = simple_gpt_analysis(text, config)
            
            # Sonucu kaydet
            output_file = output_dir / f"{file_path.stem}_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_result)
            
            print(f"  ✅ Sonuç kaydedildi: {output_file.name}")
            
            # Sonucu göster
            try:
                result_data = json.loads(json_result)
                print(f"  💰 Tutar: {result_data['fatura_tutari']} TL")
                print(f"  🏷️  Kategori: {result_data['kategori']}")
                print(f"  🏢 Firma: {result_data['firma_adi']}")
            except:
                pass
            
            processed += 1
        
        print(f"\n🎉 İşlem tamamlandı!")
        print(f"✅ {processed} dosya başarıyla işlendi")
        print(f"📂 Sonuçlar: ../../veri/duzeltme/ klasöründe")
        
    except Exception as e:
        print(f"❌ Hata: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
