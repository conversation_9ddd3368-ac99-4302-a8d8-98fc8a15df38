<?php
/**
 * BILLVAT File Upload Fix Script
 * Dosya yükleme sorunlarını tespit eder ve çözer
 */

echo "🔧 BILLVAT File Upload Fix Starting...\n";

// 1. PHP Extensions kontrolü
echo "📋 PHP Extensions kontrolü:\n";
$required_extensions = ['zip', 'gd', 'curl', 'json', 'mbstring'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext - OK\n";
    } else {
        echo "❌ $ext - MISSING\n";
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    echo "\n⚠️  Eksik PHP extensions tespit edildi!\n";
    echo "Kurulması gereken: " . implode(', ', $missing_extensions) . "\n";
}

// 2. Dosya izinleri kontrolü
echo "\n📁 Dosya izinleri kontrolü:\n";
$directories = [
    '/var/www/html/veri',
    '/var/www/html/veri/kullanicilar',
    '/var/www/html/logs',
    '/var/www/html/cache'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "📁 Dizin oluşturuldu: $dir\n";
    }
    
    if (is_writable($dir)) {
        echo "✅ $dir - Yazılabilir\n";
    } else {
        chmod($dir, 0755);
        chown($dir, 'www-data');
        chgrp($dir, 'www-data');
        echo "🔧 $dir - İzinler düzeltildi\n";
    }
}

// 3. PHP upload ayarları kontrolü
echo "\n⚙️  PHP Upload ayarları:\n";
$upload_settings = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit')
];

foreach ($upload_settings as $setting => $value) {
    echo "📊 $setting: $value\n";
}

// 4. API endpoint kontrolü
echo "\n🔗 API endpoint kontrolü:\n";
$api_file = '/var/www/html/api.php';
if (file_exists($api_file)) {
    echo "✅ api.php dosyası mevcut\n";
    
    // uploadFiles action'ını kontrol et
    $api_content = file_get_contents($api_file);
    if (strpos($api_content, 'handleUploadFiles') !== false) {
        echo "✅ handleUploadFiles fonksiyonu mevcut\n";
    } else {
        echo "❌ handleUploadFiles fonksiyonu bulunamadı\n";
    }
} else {
    echo "❌ api.php dosyası bulunamadı\n";
}

// 5. Test dosyası oluştur
echo "\n🧪 Test dosyası oluşturuluyor:\n";
$test_content = "BILLVAT Upload Test - " . date('Y-m-d H:i:s');
$test_file = '/var/www/html/veri/upload_test.txt';
if (file_put_contents($test_file, $test_content)) {
    echo "✅ Test dosyası oluşturuldu: $test_file\n";
    unlink($test_file);
    echo "✅ Test dosyası silindi - Upload dizini çalışıyor\n";
} else {
    echo "❌ Test dosyası oluşturulamadı - Upload sorunu var\n";
}

// 6. Session kontrolü
echo "\n🔐 Session kontrolü:\n";
session_start();
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session çalışıyor\n";
} else {
    echo "❌ Session sorunu var\n";
}

echo "\n✅ File Upload Fix tamamlandı!\n";
?>
