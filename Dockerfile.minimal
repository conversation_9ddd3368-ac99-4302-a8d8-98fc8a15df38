# BILLVAT Minimal Docker Image
FROM php:8.2-apache

# Install system packages
RUN apt-get update && apt-get install -y \
    libzip-dev \
    zip \
    unzip \
    curl \
    git \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libonig-dev \
    libxml2-dev \
    python3 \
    python3-pip \
    python3-venv \
    tesseract-ocr \
    tesseract-ocr-tur \
    tesseract-ocr-eng \
    poppler-utils \
    imagemagick \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd zip mbstring pdo pdo_sqlite sqlite3 exif \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Set working directory
WORKDIR /var/www/html

# Create minimal composer.json
RUN echo '{"require":{"aws/aws-sdk-php":"^3.0","league/flysystem":"^3.0","league/flysystem-aws-s3-v3":"^3.0"}}' > composer.json

# Install composer packages
RUN composer install --no-dev --optimize-autoloader

# Copy application files
COPY . .

# Create required directories
RUN mkdir -p /var/www/html/veri \
    && mkdir -p /var/www/html/logs \
    && mkdir -p /var/www/html/config \
    && mkdir -p /var/www/html/cache \
    && mkdir -p /var/www/html/database

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/veri \
    && chmod -R 777 /var/www/html/logs \
    && chmod -R 777 /var/www/html/config \
    && chmod -R 777 /var/www/html/cache \
    && chmod -R 777 /var/www/html/database

# Basic Apache configuration
RUN echo '<VirtualHost *:80>\n\
    DocumentRoot /var/www/html\n\
    <Directory /var/www/html>\n\
        AllowOverride All\n\
        Require all granted\n\
    </Directory>\n\
    ErrorLog ${APACHE_LOG_DIR}/error.log\n\
    CustomLog ${APACHE_LOG_DIR}/access.log combined\n\
</VirtualHost>' > /etc/apache2/sites-available/000-default.conf

# PHP configuration
RUN echo 'memory_limit = 256M\n\
max_execution_time = 300\n\
max_input_time = 300\n\
post_max_size = 50M\n\
upload_max_filesize = 50M\n\
max_file_uploads = 20\n\
date.timezone = Europe/Istanbul' > /usr/local/etc/php/conf.d/billvat.ini

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health.php || exit 1

# Expose port
EXPOSE 80

# Start Apache
CMD ["apache2-foreground"]
