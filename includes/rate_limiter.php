<?php
/**
 * BILLVAT Rate Limiter
 * API isteklerini sınırlandırma
 */

class RateLimiter {
    private $cache;
    private $limits;
    
    public function __construct() {
        $this->cache = new CacheManager('cache/rate_limits', 3600);
        
        // Varsayılan limitler
        $this->limits = [
            'login' => ['requests' => 5, 'window' => 300], // 5 deneme / 5 dakika
            'upload' => ['requests' => 50, 'window' => 3600], // 50 upload / 1 saat
            'api' => ['requests' => 1000, 'window' => 3600], // 1000 API çağrısı / 1 saat
            'download' => ['requests' => 100, 'window' => 3600] // 100 indirme / 1 saat
        ];
    }
    
    /**
     * Rate limit kontrolü
     */
    public function check($identifier, $action = 'api') {
        $key = $this->getKey($identifier, $action);
        $limit = $this->limits[$action] ?? $this->limits['api'];
        
        $current = $this->cache->get($key) ?? [
            'count' => 0,
            'reset_time' => time() + $limit['window']
        ];
        
        // Zaman penceresi sıfırlandı mı?
        if (time() >= $current['reset_time']) {
            $current = [
                'count' => 0,
                'reset_time' => time() + $limit['window']
            ];
        }
        
        // Limit aşıldı mı?
        if ($current['count'] >= $limit['requests']) {
            return [
                'allowed' => false,
                'limit' => $limit['requests'],
                'remaining' => 0,
                'reset_time' => $current['reset_time'],
                'retry_after' => $current['reset_time'] - time()
            ];
        }
        
        // İsteği say
        $current['count']++;
        $this->cache->set($key, $current, $limit['window']);
        
        return [
            'allowed' => true,
            'limit' => $limit['requests'],
            'remaining' => $limit['requests'] - $current['count'],
            'reset_time' => $current['reset_time'],
            'retry_after' => 0
        ];
    }
    
    /**
     * Rate limit key oluştur
     */
    private function getKey($identifier, $action) {
        return "rate_limit_{$action}_{$identifier}";
    }
    
    /**
     * IP bazlı rate limit
     */
    public function checkIP($action = 'api') {
        $ip = $this->getClientIP();
        return $this->check($ip, $action);
    }
    
    /**
     * Kullanıcı bazlı rate limit
     */
    public function checkUser($user_id, $action = 'api') {
        return $this->check("user_{$user_id}", $action);
    }
    
    /**
     * Rate limit sıfırla
     */
    public function reset($identifier, $action = 'api') {
        $key = $this->getKey($identifier, $action);
        return $this->cache->delete($key);
    }
    
    /**
     * Client IP adresini al
     */
    private function getClientIP() {
        $ip_headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Virgülle ayrılmış IP'ler varsa ilkini al
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // IP geçerli mi kontrol et
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Rate limit istatistikleri
     */
    public function getStats($identifier = null, $action = null) {
        if ($identifier && $action) {
            $key = $this->getKey($identifier, $action);
            return $this->cache->get($key);
        }
        
        // Tüm rate limit verilerini al
        $stats = [];
        $cache_files = glob('cache/rate_limits/*.cache');
        
        foreach ($cache_files as $file) {
            $key = basename($file, '.cache');
            $data = $this->cache->get($key);
            
            if ($data) {
                $stats[$key] = $data;
            }
        }
        
        return $stats;
    }
    
    /**
     * HTTP headers ekle
     */
    public function addHeaders($result) {
        header("X-RateLimit-Limit: " . $result['limit']);
        header("X-RateLimit-Remaining: " . $result['remaining']);
        header("X-RateLimit-Reset: " . $result['reset_time']);
        
        if (!$result['allowed']) {
            header("Retry-After: " . $result['retry_after']);
            http_response_code(429);
        }
    }
}

/**
 * Rate limiter middleware
 */
function checkRateLimit($action = 'api', $user_id = null) {
    $limiter = new RateLimiter();
    
    // Kullanıcı bazlı kontrol
    if ($user_id) {
        $result = $limiter->checkUser($user_id, $action);
    } else {
        // IP bazlı kontrol
        $result = $limiter->checkIP($action);
    }
    
    // Headers ekle
    $limiter->addHeaders($result);
    
    // Limit aşıldıysa hata döndür
    if (!$result['allowed']) {
        echo json_encode([
            'success' => false,
            'message' => 'Rate limit aşıldı. Lütfen ' . $result['retry_after'] . ' saniye bekleyin.',
            'retry_after' => $result['retry_after']
        ]);
        exit;
    }
    
    return $result;
}
?>
