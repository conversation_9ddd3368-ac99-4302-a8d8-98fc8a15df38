<?php
/**
 * BILLVAT Performance Monitor
 * Sistem performansını izleme
 */

class PerformanceMonitor {
    private $start_time;
    private $start_memory;
    private $checkpoints;
    
    public function __construct() {
        $this->start_time = microtime(true);
        $this->start_memory = memory_get_usage(true);
        $this->checkpoints = [];
    }
    
    /**
     * Checkpoint ekle
     */
    public function checkpoint($name) {
        $this->checkpoints[$name] = [
            'time' => microtime(true),
            'memory' => memory_get_usage(true),
            'elapsed' => microtime(true) - $this->start_time,
            'memory_diff' => memory_get_usage(true) - $this->start_memory
        ];
    }
    
    /**
     * Performans raporu al
     */
    public function getReport() {
        $total_time = microtime(true) - $this->start_time;
        $total_memory = memory_get_usage(true) - $this->start_memory;
        $peak_memory = memory_get_peak_usage(true);
        
        return [
            'total_execution_time' => round($total_time * 1000, 2), // ms
            'total_memory_usage' => $this->formatBytes($total_memory),
            'peak_memory_usage' => $this->formatBytes($peak_memory),
            'checkpoints' => $this->checkpoints,
            'system_info' => $this->getSystemInfo()
        ];
    }
    
    /**
     * Sistem bilgileri
     */
    private function getSystemInfo() {
        return [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'server_load' => $this->getServerLoad(),
            'disk_usage' => $this->getDiskUsage()
        ];
    }
    
    /**
     * Sunucu yükü
     */
    private function getServerLoad() {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }
        
        return null;
    }
    
    /**
     * Disk kullanımı
     */
    private function getDiskUsage() {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;
        
        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'usage_percent' => round(($used / $total) * 100, 2)
        ];
    }
    
    /**
     * Byte formatla
     */
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Slow query tespiti
     */
    public function isSlowRequest($threshold = 1000) { // 1 saniye
        $elapsed = (microtime(true) - $this->start_time) * 1000;
        return $elapsed > $threshold;
    }
    
    /**
     * Memory leak tespiti
     */
    public function hasMemoryLeak($threshold = 50) { // 50MB
        $memory_usage = memory_get_usage(true) - $this->start_memory;
        return $memory_usage > ($threshold * 1024 * 1024);
    }
    
    /**
     * Performans uyarısı
     */
    public function checkPerformanceIssues() {
        $issues = [];
        
        // Yavaş istek
        if ($this->isSlowRequest()) {
            $issues[] = [
                'type' => 'slow_request',
                'message' => 'Request execution time exceeded 1 second',
                'value' => round((microtime(true) - $this->start_time) * 1000, 2) . 'ms'
            ];
        }
        
        // Memory leak
        if ($this->hasMemoryLeak()) {
            $issues[] = [
                'type' => 'memory_leak',
                'message' => 'Memory usage exceeded 50MB',
                'value' => $this->formatBytes(memory_get_usage(true) - $this->start_memory)
            ];
        }
        
        // Yüksek CPU kullanımı
        $load = $this->getServerLoad();
        if ($load && $load['1min'] > 2.0) {
            $issues[] = [
                'type' => 'high_cpu',
                'message' => 'Server load is high',
                'value' => $load['1min']
            ];
        }
        
        // Düşük disk alanı
        $disk = $this->getDiskUsage();
        if ($disk['usage_percent'] > 90) {
            $issues[] = [
                'type' => 'low_disk_space',
                'message' => 'Disk usage is above 90%',
                'value' => $disk['usage_percent'] . '%'
            ];
        }
        
        return $issues;
    }
    
    /**
     * Performans logla
     */
    public function logPerformance($action = 'unknown') {
        $report = $this->getReport();
        $issues = $this->checkPerformanceIssues();
        
        $log_data = [
            'action' => $action,
            'execution_time' => $report['total_execution_time'],
            'memory_usage' => $report['total_memory_usage'],
            'peak_memory' => $report['peak_memory_usage'],
            'issues' => $issues,
            'user_id' => $_SESSION['user_id'] ?? null,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        // Performans sorunları varsa warning log
        if (!empty($issues)) {
            log_warning('Performance issues detected', $log_data);
        } else {
            log_info('Performance metrics', $log_data);
        }
        
        return $log_data;
    }
}

/**
 * Global performance monitor
 */
function performance_monitor() {
    static $instance = null;
    
    if ($instance === null) {
        $instance = new PerformanceMonitor();
    }
    
    return $instance;
}

/**
 * Performance helper fonksiyonları
 */
function perf_checkpoint($name) {
    performance_monitor()->checkpoint($name);
}

function perf_report() {
    return performance_monitor()->getReport();
}

function perf_log($action = 'unknown') {
    return performance_monitor()->logPerformance($action);
}
?>
