<?php
session_start();

// Zaten giriş yapmışsa dashboard'a yönlendir
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BILLVAT AI - Giriş</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .google-btn {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            transition: all 0.3s ease;
        }
        .google-btn:hover {
            background: #357ae8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }
        .form-control {
            border-radius: 50px;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .role-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 0.7em;
        }
        .hierarchy-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        /* Login Responsive Styles */
        @media (max-width: 767px) {
            body {
                padding: 1rem;
                align-items: flex-start;
                padding-top: 2rem;
            }

            .login-card {
                border-radius: 15px;
                margin-bottom: 2rem;
            }

            .login-header {
                border-radius: 15px 15px 0 0;
                padding: 1.5rem !important;
            }

            .login-header h3 {
                font-size: 1.5rem;
            }

            .login-header p {
                font-size: 0.9rem;
            }

            .card-body {
                padding: 2rem 1.5rem !important;
            }

            .form-control {
                padding: 10px 15px;
                font-size: 0.9rem;
            }

            .btn-primary,
            .google-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .hierarchy-info {
                padding: 15px;
                margin-top: 15px;
            }

            .hierarchy-info .col-4 {
                margin-bottom: 1rem;
            }

            .hierarchy-info .p-2 {
                padding: 0.75rem !important;
                font-size: 0.8rem;
            }

            .role-badge {
                font-size: 0.6em;
                top: -5px;
                right: -5px;
            }
        }

        @media (max-width: 575px) {
            body {
                padding: 0.5rem;
                padding-top: 1rem;
            }

            .login-header {
                padding: 1rem !important;
            }

            .login-header h3 {
                font-size: 1.25rem;
            }

            .login-header p {
                font-size: 0.8rem;
            }

            .card-body {
                padding: 1.5rem 1rem !important;
            }

            .form-control {
                padding: 8px 12px;
                font-size: 0.85rem;
            }

            .btn-primary,
            .google-btn {
                padding: 8px 15px;
                font-size: 0.85rem;
            }

            .hierarchy-info {
                padding: 10px;
            }

            .hierarchy-info h6 {
                font-size: 0.9rem;
            }

            .hierarchy-info .p-2 {
                padding: 0.5rem !important;
                font-size: 0.7rem;
            }

            .hierarchy-info small {
                font-size: 0.65rem;
            }
        }

        /* Landscape phone adjustments */
        @media (max-width: 767px) and (orientation: landscape) {
            body {
                padding-top: 1rem;
            }

            .login-card {
                max-width: 600px;
                margin: 0 auto;
            }

            .hierarchy-info .row {
                justify-content: center;
            }

            .hierarchy-info .col-4 {
                flex: 0 0 auto;
                width: auto;
                margin: 0 1rem;
            }
        }

        /* Touch-friendly adjustments */
        @media (hover: none) and (pointer: coarse) {
            .form-control {
                min-height: 44px;
            }

            .btn-primary,
            .google-btn {
                min-height: 44px;
                min-width: 44px;
            }

            .form-check-input {
                width: 1.25em;
                height: 1.25em;
            }
        }

        /* Large screen enhancements */
        @media (min-width: 992px) {
            .login-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            }

            .card-body {
                padding: 3rem !important;
            }

            .hierarchy-info {
                padding: 2rem;
            }
        }
    </style>

    <!-- Responsive CSS -->
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header text-center p-4">
                        <h3 class="mb-0">
                            <i class="fas fa-robot"></i> BILLVAT AI
                        </h3>
                        <p class="mb-0">OCR Invoice Processing System</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <!-- Hata/Başarı Mesajları -->
                        <div id="message-container"></div>
                        
                        <!-- Login Form -->
                        <form id="loginForm">
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-envelope"></i> Email
                                </label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-lock"></i> Şifre
                                </label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="remember_me" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Beni Hatırla
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Giriş Yap
                                </button>
                            </div>
                        </form>
                        
                        <!-- Google Authentication (Geçici olarak devre dışı) -->
                        <div class="text-center mb-4" style="display: none;">
                            <div class="position-relative">
                                <hr>
                                <span class="position-absolute top-50 start-50 translate-middle bg-white px-3 text-muted">
                                    veya
                                </span>
                            </div>
                        </div>

                        <div class="d-grid mb-4" style="display: none;">
                            <button type="button" class="google-btn" disabled>
                                <i class="fab fa-google me-2"></i> Google ile Giriş (Yakında)
                            </button>
                        </div>
                        
                        <!-- Sistem Bilgileri -->
                        <div class="hierarchy-info">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-info-circle"></i> Sistem Hiyerarşisi
                            </h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="position-relative">
                                        <div class="p-2 bg-danger text-white rounded">
                                            <i class="fas fa-crown"></i><br>
                                            <small>ADMIN</small>
                                        </div>
                                        <span class="role-badge badge bg-danger">1</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="position-relative">
                                        <div class="p-2 bg-warning text-dark rounded">
                                            <i class="fas fa-edit"></i><br>
                                            <small>EDITOR</small>
                                        </div>
                                        <span class="role-badge badge bg-warning">2</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="position-relative">
                                        <div class="p-2 bg-success text-white rounded">
                                            <i class="fas fa-user"></i><br>
                                            <small>USER</small>
                                        </div>
                                        <span class="role-badge badge bg-success">3</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    Üyelik sadece davet ile mümkündür
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Login Form Submit
        $('#loginForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            $.ajax({
                url: 'api.php?action=login',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        showMessage('Giriş başarılı! Yönlendiriliyorsunuz...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.php';
                        }, 1500);
                    } else {
                        showMessage(response.message, 'danger');
                    }
                },
                error: function() {
                    showMessage('Bir hata oluştu!', 'danger');
                }
            });
        });
        

        
        // Mesaj gösterme fonksiyonu
        function showMessage(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#message-container').html(alertHtml);
        }
    </script>
</body>
</html>
