# BILLVAT Production Deployment Guide

## 🚀 Production Deployment for billvat.com

This guide covers the complete production deployment of BILLVAT with Docker, SSL certificates, and multi-storage support.

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 20GB, Recommended 50GB+
- **CPU**: 2+ cores recommended
- **Network**: Public IP with domain pointing to server

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- Curl
- UFW (recommended)

### Domain Setup
- Domain: `billvat.com`
- DNS A record pointing to server IP
- Optional: `www.billvat.com` CNAME to `billvat.com`

## 🔧 Quick Installation

### 1. Clone Repository
```bash
git clone https://github.com/your-repo/billvat.git
cd billvat
```

### 2. Make Scripts Executable
```bash
chmod +x *.sh
chmod +x docker/*.sh
```

### 3. Full Production Deployment
```bash
# Full deployment with SSL
sudo ./deploy-production.sh full

# Or step by step:
sudo ./ssl-setup.sh
sudo ./deploy-production.sh update
```

### 4. Access Application
- **Main Site**: https://billvat.com
- **Health Check**: https://billvat.com/health.php
- **Admin Panel**: https://billvat.com/admin_panel.php

## 🔒 SSL Certificate Setup

### Automatic SSL with Let's Encrypt
```bash
# Production SSL certificate
sudo ./ssl-setup.sh

# Test with staging (for testing)
sudo ./ssl-setup.sh staging
```

### Manual SSL Setup
```bash
# Start temporary nginx for ACME challenge
docker-compose -f docker-compose.ssl.yml up -d

# Generate certificate
docker-compose -f docker-compose.ssl.yml run --rm certbot \
  certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d billvat.com \
  -d www.billvat.com

# Start production services
docker-compose -f docker-compose.prod.yml up -d
```

## 🐳 Docker Management

### Production Commands
```bash
# Start services
docker-compose -f docker-compose.prod.yml up -d

# Stop services
docker-compose -f docker-compose.prod.yml down

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Restart specific service
docker-compose -f docker-compose.prod.yml restart nginx

# Update and restart
./deploy-production.sh update
```

### Container Management
```bash
# Enter application container
docker-compose -f docker-compose.prod.yml exec billvat-app bash

# Enter nginx container
docker-compose -f docker-compose.prod.yml exec nginx sh

# Check container status
docker-compose -f docker-compose.prod.yml ps

# View resource usage
docker stats
```

## ⚙️ Configuration

### Environment Variables
Copy and edit the production environment file:
```bash
cp .env.production .env
nano .env
```

Key settings to configure:
```bash
# Domain
DOMAIN_NAME=billvat.com
APP_URL=https://billvat.com

# Storage (configure in admin panel)
STORAGE_TYPE=local  # or s3, wasabi

# Security
REDIS_PASSWORD=your_secure_password
SESSION_SECURE=true

# SSL
SSL_ENABLED=true
LETSENCRYPT_EMAIL=<EMAIL>
```

### Storage Configuration
Configure storage in the admin panel:
1. Login as admin
2. Go to "Storage Ayarları"
3. Choose storage type:
   - **Local**: Default, no configuration needed
   - **Amazon S3**: Enter AWS credentials
   - **Wasabi**: Enter Wasabi credentials

## 🔐 Security Setup

### Firewall Configuration
```bash
# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow SSH (if needed)
sudo ufw allow 22/tcp

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status
```

### SSL Security Test
Test your SSL configuration:
```bash
# SSL Labs test
https://www.ssllabs.com/ssltest/analyze.html?d=billvat.com

# Local SSL test
curl -I https://billvat.com
```

### Default Credentials
**⚠️ IMPORTANT: Change these immediately after deployment!**

- **Admin Email**: <EMAIL>
- **Admin Password**: admin123

## 📊 Monitoring

### Health Check
```bash
# Check application health
curl -s https://billvat.com/health.php | jq

# Expected response
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00+00:00",
  "version": "1.0.0",
  "services": {
    "storage_config": "ok",
    "data_directory": "ok",
    "redis": "ok",
    "file_permissions": "ok",
    "php_extensions": "ok"
  }
}
```

### Log Monitoring
```bash
# Application logs
docker-compose -f docker-compose.prod.yml logs billvat-app

# Nginx logs
docker-compose -f docker-compose.prod.yml logs nginx

# SSL certificate logs
docker-compose -f docker-compose.prod.yml logs certbot

# System logs
tail -f /var/log/syslog
```

### Performance Monitoring
```bash
# Container resource usage
docker stats

# Disk usage
df -h

# Memory usage
free -h

# Network connections
netstat -tulpn | grep :443
```

## 🔄 Maintenance

### SSL Certificate Renewal
Certificates auto-renew every 12 hours. Manual renewal:
```bash
# Test renewal
docker-compose -f docker-compose.prod.yml exec certbot certbot renew --dry-run

# Force renewal
docker-compose -f docker-compose.prod.yml exec certbot certbot renew --force-renewal

# Restart nginx after renewal
docker-compose -f docker-compose.prod.yml restart nginx
```

### Backup and Restore
```bash
# Create backup
./deploy-production.sh backup

# Restore from backup
./deploy-production.sh restore /backups/billvat/backup_20240115.tar.gz

# Automated daily backup (add to crontab)
0 2 * * * /opt/billvat/deploy-production.sh backup
```

### Updates
```bash
# Update application
./deploy-production.sh update

# Rollback if needed
./deploy-production.sh rollback
```

## 🐛 Troubleshooting

### Common Issues

#### SSL Certificate Issues
```bash
# Check certificate status
docker-compose -f docker-compose.prod.yml exec certbot certbot certificates

# Check nginx SSL config
docker-compose -f docker-compose.prod.yml exec nginx nginx -t

# Regenerate certificate
./ssl-setup.sh
```

#### Container Issues
```bash
# Check container logs
docker-compose -f docker-compose.prod.yml logs [service_name]

# Restart all services
docker-compose -f docker-compose.prod.yml restart

# Rebuild and restart
docker-compose -f docker-compose.prod.yml up -d --build --force-recreate
```

#### Storage Issues
```bash
# Check storage configuration
curl -s https://billvat.com/health.php | jq '.storage_type'

# Test storage connection (admin panel)
# Go to Storage Ayarları > Test Connection
```

#### Performance Issues
```bash
# Check resource usage
docker stats

# Check disk space
df -h

# Check memory
free -h

# Optimize containers
docker system prune -f
```

### Emergency Procedures

#### Service Down
```bash
# Quick restart
docker-compose -f docker-compose.prod.yml restart

# Full restart
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d
```

#### SSL Expired
```bash
# Emergency HTTP access (temporary)
# Edit nginx config to allow HTTP temporarily
# Then fix SSL and re-enable HTTPS redirect
```

#### Data Recovery
```bash
# Restore from latest backup
./deploy-production.sh rollback

# Or manual restore
tar -xzf /backups/billvat/latest_backup.tar.gz
docker-compose -f docker-compose.prod.yml restart
```

## 📞 Support

### Log Locations
- **Application**: `docker-compose logs billvat-app`
- **Nginx**: `docker-compose logs nginx`
- **SSL**: `docker-compose logs certbot`
- **System**: `/var/log/syslog`

### Health Endpoints
- **Main**: https://billvat.com/health.php
- **Nginx**: https://billvat.com/nginx_status (if enabled)

### Configuration Files
- **Main Config**: `.env`
- **Storage Config**: `config/storage.json`
- **Nginx Config**: `docker/nginx-prod.conf`
- **SSL Config**: `docker/ssl-params.conf`

---

**Production Deployment Complete! 🎉**

Your BILLVAT instance is now running securely at https://billvat.com with automatic SSL renewal and multi-storage support.
