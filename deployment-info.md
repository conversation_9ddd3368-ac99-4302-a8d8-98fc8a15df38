# 🚀 DEPLOYMENT BİLGİLERİ

## 📡 SSH Sunucu Bilgileri
- **IP Adresi**: **************
- **Kullanıcı**: root
- **Şifre**: 2LDZonI46

## 📁 Sunucu Dizin Bilgileri
- **<PERSON><PERSON><PERSON>**: /var/www/html/ai99
- **<PERSON><PERSON>a Sahibi**: www-data
- **Grup**: www-data

## 🔧 Tek SSH Oturumu Deployment Komutları

### 1. Tek Komut ile Tüm Transfer (Sadece 1 Kere Şifre)
```bash
# Önce local'den sunucuya dosyaları transfer et
scp -r -o StrictHostKeyChecking=no . root@**************:/tmp/billvat_temp/

# Sonra SSH ile tek oturumda tüm işlemleri yap
ssh -o StrictHostKeyChecking=no root@************** "
mkdir -p /var/www/html/ai99/{js,languages,includes,backend,veri} && \
cp -r /tmp/billvat_temp/*.php /var/www/html/ai99/ && \
cp -r /tmp/billvat_temp/*.txt /var/www/html/ai99/ && \
cp -r /tmp/billvat_temp/*.json /var/www/html/ai99/ && \
cp -r /tmp/billvat_temp/*.md /var/www/html/ai99/ && \
cp -r /tmp/billvat_temp/js/* /var/www/html/ai99/js/ && \
cp -r /tmp/billvat_temp/languages/* /var/www/html/ai99/languages/ && \
cp -r /tmp/billvat_temp/includes/* /var/www/html/ai99/includes/ && \
cp -r /tmp/billvat_temp/backend/* /var/www/html/ai99/backend/ && \
cp -r /tmp/billvat_temp/veri/* /var/www/html/ai99/veri/ && \
chown -R www-data:www-data /var/www/html/ai99 && \
rm -rf /tmp/billvat_temp && \
echo 'Deployment tamamlandı!'
"
```

### 2. Alternatif: SCP + SSH Birleşik
```bash
# Tüm dosyaları tek seferde transfer et, sonra izinleri düzenle
scp -r -o StrictHostKeyChecking=no . root@**************:/var/www/html/ai99/ && \
ssh -o StrictHostKeyChecking=no root@************** "chown -R www-data:www-data /var/www/html/ai99 && echo 'Deployment tamamlandı!'"
```

### 5. Web Sunucusu Yeniden Başlatma (gerekirse)
```bash
systemctl restart apache2
# veya
systemctl restart nginx
```

## 📋 Deployment Checklist

- [ ] SSH bağlantısı kuruldu
- [ ] Hedef dizine gidildi
- [ ] Git ile dosyalar çekildi
- [ ] Dosya sahiplikleri düzenlendi (www-data:www-data)
- [ ] Dizin izinleri ayarlandı (755)
- [ ] Dosya izinleri ayarlandı (644)
- [ ] Özel dizin izinleri kontrol edildi
- [ ] Web sunucusu yeniden başlatıldı (gerekirse)
- [ ] Web sitesi test edildi

## 🎯 Deployment Tetikleyici Komut

Kullanıcı **"deploy et"** veya **"işlemler bitti artık servera aktar"** dediğinde:
1. SCP ile dosyaları sunucuya aktar (manuel şifre girişi)
2. SSH ile dosya izinlerini düzenle (manuel şifre girişi)
3. "tamamlandı" mesajı ver (hata yoksa)

**NOT:** Windows'ta otomatik şifre girişi sorunlu olduğu için manuel şifre girişi kullanılacak.
Şifre: **2LDZonI46**

### Otomatik Deployment Sırası:
1. Dizin oluştur
2. Ana dosyaları transfer et
3. JS dizinini transfer et
4. Languages dizinini transfer et
5. Includes dizinini transfer et
6. Backend dizinini transfer et
7. Veri dizinini transfer et
8. Dosya izinlerini düzenle
9. "tamamlandı" mesajı ver

## 📝 Notlar

- Backup alma gerekirse /var/www/html/ai99 dizinini yedekle
- Database değişiklikleri varsa ayrıca migrate et
- Config dosyalarını kontrol et (API keys, database bağlantıları vs.)
