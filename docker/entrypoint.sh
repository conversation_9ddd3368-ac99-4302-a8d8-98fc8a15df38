#!/bin/bash
set -e

echo "🚀 BILLVAT Container Starting..."

# Dizinleri oluştur
mkdir -p /var/www/html/veri/kullanicilar
mkdir -p /var/www/html/logs
mkdir -p /var/www/html/config
mkdir -p /var/www/html/cache

# İzinleri ayarla
chown -R www-data:www-data /var/www/html/veri
chown -R www-data:www-data /var/www/html/logs
chown -R www-data:www-data /var/www/html/config
chown -R www-data:www-data /var/www/html/cache

chmod -R 755 /var/www/html
chmod -R 777 /var/www/html/veri
chmod -R 777 /var/www/html/logs
chmod -R 777 /var/www/html/cache

# Varsayılan config dosyasını oluştur
if [ ! -f /var/www/html/config/storage.json ]; then
    echo "📁 Creating default storage configuration..."
    cat > /var/www/html/config/storage.json << EOF
{
    "storage_type": "${STORAGE_TYPE:-local}",
    "local": {
        "path": "/var/www/html/veri"
    },
    "s3": {
        "access_key": "${AWS_ACCESS_KEY_ID:-}",
        "secret_key": "${AWS_SECRET_ACCESS_KEY:-}",
        "region": "${AWS_DEFAULT_REGION:-us-east-1}",
        "bucket": "${S3_BUCKET:-}",
        "endpoint": "${S3_ENDPOINT:-}"
    },
    "wasabi": {
        "access_key": "${WASABI_ACCESS_KEY:-}",
        "secret_key": "${WASABI_SECRET_KEY:-}",
        "region": "${WASABI_REGION:-us-east-1}",
        "bucket": "${WASABI_BUCKET:-}",
        "endpoint": "https://s3.${WASABI_REGION:-us-east-1}.wasabisys.com"
    }
}
EOF
fi

# Varsayılan users.json oluştur
if [ ! -f /var/www/html/veri/users.json ]; then
    echo "👥 Creating default users..."
    cat > /var/www/html/veri/users.json << EOF
{
    "admin_$(date +%s)": {
        "id": "admin_$(date +%s)",
        "email": "<EMAIL>",
        "password": "\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
        "full_name": "System Administrator",
        "role": "admin",
        "folder": "admin_$(date +%s)",
        "created_at": "$(date '+%Y-%m-%d %H:%M:%S')",
        "last_login": null,
        "is_active": true,
        "description": "System Administrator"
    }
}
EOF
fi

# Health check endpoint oluştur
cat > /var/www/html/health.php << 'EOF'
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'healthy',
    'timestamp' => date('c'),
    'version' => '1.0.0',
    'services' => []
];

// Storage kontrolü
$storage_config = '/var/www/html/config/storage.json';
if (file_exists($storage_config)) {
    $health['services']['storage'] = 'ok';
} else {
    $health['services']['storage'] = 'error';
    $health['status'] = 'unhealthy';
}

// Veri dizini kontrolü
if (is_writable('/var/www/html/veri')) {
    $health['services']['data_directory'] = 'ok';
} else {
    $health['services']['data_directory'] = 'error';
    $health['status'] = 'unhealthy';
}

// Redis kontrolü (opsiyonel)
if (class_exists('Redis')) {
    try {
        $redis = new Redis();
        $redis->connect('redis', 6379);
        $redis->ping();
        $health['services']['redis'] = 'ok';
        $redis->close();
    } catch (Exception $e) {
        $health['services']['redis'] = 'error';
    }
}

http_response_code($health['status'] === 'healthy' ? 200 : 503);
echo json_encode($health, JSON_PRETTY_PRINT);
?>
EOF

echo "✅ BILLVAT Container Ready!"
echo "📊 Health Check: http://localhost/health.php"
echo "🌐 Application: http://localhost"

# Apache'yi başlat
exec "$@"
