events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;
    server_tokens off;

    # Buffer Settings
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        font/opentype;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

    # Upstream
    upstream billvat_app {
        server billvat-app:80;
        keepalive 32;
    }

    # HTTP Server (Redirect to HTTPS + Let's Encrypt)
    server {
        listen 80;
        server_name billvat.com www.billvat.com;
        
        # Let's Encrypt challenge
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
            try_files $uri =404;
        }
        
        # Health check endpoint (HTTP only)
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Redirect all other HTTP to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS Server - billvat.com
    server {
        listen 443 ssl http2;
        server_name billvat.com;

        # SSL Configuration - Let's Encrypt
        ssl_certificate /etc/letsencrypt/live/billvat.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/billvat.com/privkey.pem;
        
        # SSL Security Settings
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_session_tickets off;
        
        # OCSP Stapling
        ssl_stapling on;
        ssl_stapling_verify on;
        ssl_trusted_certificate /etc/letsencrypt/live/billvat.com/chain.pem;
        resolver ******* ******* valid=300s;
        resolver_timeout 5s;

        # Security Headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://code.jquery.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self' https://api.qrserver.com;" always;
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

        # Root directory
        root /var/www/html;
        index index.php index.html;

        # API Rate Limiting
        location /api.php {
            limit_req zone=api burst=20 nodelay;
            try_files $uri =404;
            
            fastcgi_pass billvat_app;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTPS on;
            fastcgi_param SERVER_NAME $host;
            include fastcgi_params;
            
            # Upload specific settings
            fastcgi_read_timeout 300;
            fastcgi_send_timeout 300;
        }

        # Login Rate Limiting
        location /login.php {
            limit_req zone=login burst=5 nodelay;
            try_files $uri =404;
            
            fastcgi_pass billvat_app;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTPS on;
            fastcgi_param SERVER_NAME $host;
            include fastcgi_params;
        }

        # Upload endpoints
        location ~ ^/(upload|api\.php\?.*upload) {
            limit_req zone=upload burst=5 nodelay;
            
            client_max_body_size 50M;
            client_body_timeout 300s;
            
            try_files $uri =404;
            fastcgi_pass billvat_app;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTPS on;
            fastcgi_param SERVER_NAME $host;
            include fastcgi_params;
            
            fastcgi_read_timeout 300;
            fastcgi_send_timeout 300;
        }

        # PHP Files
        location ~ \.php$ {
            try_files $uri =404;
            
            fastcgi_pass billvat_app;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTPS on;
            fastcgi_param SERVER_NAME $host;
            include fastcgi_params;
            
            # Security
            fastcgi_param HTTP_PROXY "";
            fastcgi_hide_header X-Powered-By;
        }

        # Static Files Caching
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
            
            # CORS for fonts
            location ~* \.(woff|woff2|ttf|eot)$ {
                add_header Access-Control-Allow-Origin "*";
            }
        }

        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ /(config|logs|cache|vendor|docker)/ {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ \.(env|json|lock|yml|yaml|sh)$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Health Check
        location /health {
            access_log off;
            try_files $uri /health.php;
        }

        # Robots.txt
        location = /robots.txt {
            access_log off;
            log_not_found off;
            return 200 "User-agent: *\nDisallow: /admin\nDisallow: /api\nDisallow: /config\nDisallow: /logs\nDisallow: /veri\n";
        }

        # Favicon
        location = /favicon.ico {
            access_log off;
            log_not_found off;
            expires 1y;
        }

        # Default location
        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }

    # HTTPS Server - www.billvat.com (Redirect to non-www)
    server {
        listen 443 ssl http2;
        server_name www.billvat.com;

        # SSL Configuration
        ssl_certificate /etc/letsencrypt/live/billvat.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/billvat.com/privkey.pem;

        # Redirect to non-www
        return 301 https://billvat.com$request_uri;
    }
}
