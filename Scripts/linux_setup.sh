#!/bin/bash

echo "========================================"
echo "  BILLVAT SİSTEMİ - LINUX KURULUM"
echo "========================================"
echo

echo "[1/4] Python sürümü kontrol ediliyor..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python bulunamadı!"
    echo "Lütfen Python 3.8+ yükleyin: apt install python3 python3-pip"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
echo "✅ Python $PYTHON_VERSION bulundu"

echo
echo "[2/4] Gerekli paketler yükleniyor..."
python3 -m pip install --quiet openai requests pathlib2 Pillow jsonschema colorlog python-dateutil psutil
if [ $? -ne 0 ]; then
    echo "❌ Paket yükleme başarısız!"
    exit 1
fi
echo "✅ Paketler başarıyla yüklendi"

echo
echo "[3/4] Sistem testleri çalıştırılıyor..."
python3 test_demo_mode.py &> /dev/null
if [ $? -ne 0 ]; then
    echo "❌ Demo testler başarısız!"
    echo "Detaylar için: python3 test_demo_mode.py"
    exit 1
fi
echo "✅ Demo testler başarılı"

echo
echo "[4/4] API bağlantıları test ediliyor..."
python3 test_api_connection.py &> /dev/null
if [ $? -eq 0 ]; then
    echo "✅ API'ler çalışıyor - sistem kullanıma hazır!"
else
    echo "⚠️  API'ler çalışmıyor (billing/quota sorunu)"
    echo "💡 Demo modunda sistem çalışıyor"
fi

chmod +x *.sh
echo
echo "Kurulum tamamlandı! Sistemi başlatmak için:"
echo "$ ./start.sh"