import json
import os
import difflib
import logging
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)

MEMORY_PATH = "veri/ogrenme.json"

def load_memory():
    """Öğrenme verilerini yükler"""
    try:
        # Veri klasörünü oluştur
        os.makedirs(os.path.dirname(MEMORY_PATH), exist_ok=True)

        if os.path.exists(MEMORY_PATH):
            with open(MEMORY_PATH, "r", encoding="utf-8") as f:
                memory_data = json.load(f)
            logger.info(f"Bellekten {len(memory_data)} kayıt yüklendi")
            return memory_data
        else:
            logger.info("Bellek dosyası bulunamadı, yeni bellek oluşturuluyor")
            return []
    except json.JSONDecodeError as e:
        logger.error(f"Bellek dosyası bozuk, yeni bellek oluşturuluyor: {e}")
        return []
    except Exception as e:
        logger.error(f"Bellek yükleme hatası: {e}")
        return []

def check_memory(full_text, threshold=0.90):
    """Bellekte benzer metin var mı kontrol eder"""
    try:
        if not full_text or not full_text.strip():
            return None

        memory = load_memory()

        # Metin hash'i ile hızlı kontrol
        text_hash = hashlib.md5(full_text.encode('utf-8')).hexdigest()

        for item in memory:
            # Önce hash kontrolü (tam eşleşme)
            if item.get("hash") == text_hash:
                logger.info("Tam eşleşen metin bellekte bulundu (hash)")
                return item["duzeltme"]

            # Sonra benzerlik kontrolü
            if "metin" in item:
                skor = difflib.SequenceMatcher(None, full_text, item["metin"]).ratio()
                if skor >= threshold:
                    logger.info(f"Benzer metin bellekte bulundu (benzerlik: {skor:.2f})")
                    return item["duzeltme"]

        logger.info("Bellekte benzer metin bulunamadı")
        return None

    except Exception as e:
        logger.error(f"Bellek kontrolü hatası: {e}")
        return None

def save_memory(full_text, json_data):
    """Yeni öğrenme verisini belleğe kaydeder"""
    try:
        if not full_text or not full_text.strip():
            logger.warning("Boş metin belleğe kaydedilmedi")
            return

        memory = load_memory()

        # Metin hash'i oluştur
        text_hash = hashlib.md5(full_text.encode('utf-8')).hexdigest()

        # Aynı hash'e sahip kayıt var mı kontrol et
        for item in memory:
            if item.get("hash") == text_hash:
                logger.info("Aynı metin zaten bellekte mevcut, güncelleniyor")
                item["duzeltme"] = json_data
                item["guncelleme_tarihi"] = datetime.now().isoformat()
                break
        else:
            # Yeni kayıt ekle
            new_entry = {
                "metin": full_text,
                "duzeltme": json_data,
                "hash": text_hash,
                "kayit_tarihi": datetime.now().isoformat(),
                "kullanim_sayisi": 1
            }
            memory.append(new_entry)
            logger.info("Yeni öğrenme verisi belleğe eklendi")

        # Bellek boyutunu kontrol et (maksimum 1000 kayıt)
        if len(memory) > 1000:
            # En eski kayıtları sil
            memory = sorted(memory, key=lambda x: x.get("kayit_tarihi", ""), reverse=True)[:1000]
            logger.info("Bellek boyutu sınırlandı (1000 kayıt)")

        # Dosyaya kaydet
        os.makedirs(os.path.dirname(MEMORY_PATH), exist_ok=True)
        with open(MEMORY_PATH, "w", encoding="utf-8") as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)

        logger.info(f"Bellek kaydedildi, toplam {len(memory)} kayıt")

    except Exception as e:
        logger.error(f"Bellek kaydetme hatası: {e}")

def get_memory_stats():
    """Bellek istatistiklerini döner"""
    try:
        memory = load_memory()
        return {
            "toplam_kayit": len(memory),
            "dosya_boyutu": os.path.getsize(MEMORY_PATH) if os.path.exists(MEMORY_PATH) else 0,
            "son_guncelleme": max([item.get("kayit_tarihi", "") for item in memory]) if memory else None
        }
    except Exception as e:
        logger.error(f"Bellek istatistikleri alınamadı: {e}")
        return {"toplam_kayit": 0, "dosya_boyutu": 0, "son_guncelleme": None}