#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Klasördeki tüm fatura ve fişleri test eder
"""

import main
from pathlib import Path
from config_loader import load_global_config
import time
import logging

# Logging seviyesini ayarla
logging.basicConfig(level=logging.INFO)

def test_all_files():
    """Klasördeki tüm dosyaları test eder"""
    
    # Global config yükle
    global_config = load_global_config()
    
    # Test klasörü
    test_dir = Path('../../veri/kullanicilar/kisi1')
    print(f'📁 Test klasörü: {test_dir}')
    
    # Tüm PDF ve görüntü dosyalarını bul
    file_extensions = ['*.pdf', '*.jpg', '*.jpeg', '*.png']
    test_files = []
    
    for ext in file_extensions:
        test_files.extend(test_dir.glob(ext))
    
    # Sistem dosyalarını filtrele
    excluded_files = ['gelirOK.pdf', 'venteOK.pdf', 'config.json', 'README.txt']
    test_files = [f for f in test_files if f.name not in excluded_files]
    
    print(f'📋 Toplam {len(test_files)} dosya bulundu:')
    for i, file in enumerate(test_files, 1):
        print(f'  {i}. {file.name}')
    
    print('\n🚀 Toplu test başlatılıyor...\n')
    
    # Test sonuçları
    results = {'success': 0, 'failed': 0, 'errors': []}
    start_time = time.time()
    
    for i, file_path in enumerate(test_files, 1):
        print(f'[{i}/{len(test_files)}] 🔍 İşleniyor: {file_path.name}')
        
        try:
            result = main.process_single_file(file_path, global_config)
            if result:
                results['success'] += 1
                print('  ✅ Başarılı!')
            else:
                results['failed'] += 1
                results['errors'].append(f'{file_path.name}: İşlem başarısız')
                print('  ❌ Başarısız!')
        except Exception as e:
            results['failed'] += 1
            results['errors'].append(f'{file_path.name}: {str(e)}')
            print(f'  💥 Hata: {str(e)}')
        
        print()
    
    # Test özeti
    end_time = time.time()
    duration = end_time - start_time
    
    print('📊 TEST ÖZETI:')
    print(f'  ✅ Başarılı: {results["success"]}')
    print(f'  ❌ Başarısız: {results["failed"]}')
    print(f'  ⏱️ Süre: {duration:.1f} saniye')
    
    if len(test_files) > 0:
        success_rate = (results["success"] / len(test_files) * 100)
        print(f'  📈 Başarı oranı: {success_rate:.1f}%')
    
    if results['errors']:
        print('\n🚨 HATALAR:')
        for error in results['errors']:
            print(f'  - {error}')
    
    return results

if __name__ == "__main__":
    test_all_files()
