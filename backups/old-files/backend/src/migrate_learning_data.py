#!/usr/bin/env python3
"""
Eski JSON öğrenme verilerini yeni SQLite sistemine taşır
"""

import sys
import os
from pathlib import Path

# Ana dizine göre yolu ayarla
sys.path.append(str(Path(__file__).parent))

from learning_system import get_learning_system

def main():
    # Eski JSON dosyası yolu
    json_file = "../../veri/ogrenme.json"
    
    if not os.path.exists(json_file):
        print(f"Eski öğrenme dosyası bulunamadı: {json_file}")
        return
    
    # Öğrenme sistemini al
    learning_system = get_learning_system()
    
    print("Eski öğrenme verilerini SQLite'a taşıyor...")
    
    # Migration işlemini başlat
    migrated_count = learning_system.migrate_from_json(json_file)
    
    if migrated_count > 0:
        print(f"✅ {migrated_count} kayıt başarıyla taşındı")
        
        # İstatistikleri göster
        stats = learning_system.get_statistics()
        print(f"📊 Toplam kayıt sayısı: {stats['total_records']}")
        print(f"📊 Ortalama güven skoru: {stats['average_confidence']}")
        print(f"📊 Kategori dağılımı: {len(stats['categories'])} farklı kategori")
        
        # Eski dosyayı yedekle
        backup_file = json_file + ".backup"
        os.rename(json_file, backup_file)
        print(f"📁 Eski dosya yedeklendi: {backup_file}")
        
    else:
        print("❌ Hiçbir kayıt taşınamadı")

if __name__ == "__main__":
    main()
