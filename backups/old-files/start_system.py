#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BILLVAT Sistemi - Evrensel Başlatıcı
Bu script tüm işletim sistemlerinde çalışır ve sistemi otomatik tespit eder.
"""

import os
import sys
import json
from pathlib import Path

def main():
    """Ana başlatıcı fonksiyon"""
    print("=" * 60)
    print("🚀 BILLVAT SİSTEMİ - EVRENSEL BAŞLATICI")
    print("=" * 60)
    
    # Sistem tespit modülünü yükle
    try:
        from system_detector import SystemDetector
        detector = SystemDetector()
        config = detector.get_config()
        
        print(f"✅ Sistem tespit edildi: {config['os_type'].upper()}")
        print(f"🐍 Python komutu: {config['python_executable']}")
        print(f"📁 Yol ayırıcı: {config['paths']['separator']}")
        print(f"🏠 Ana dizin: {config['paths']['user_home']}")
        
        # Sistem konfigürasyonunu kaydet
        project_root = Path(__file__).parent
        system_config_path = project_root / "config" / "system_config.json"
        detector.save_system_config(system_config_path)
        print(f"💾 Sistem config kaydedildi: {system_config_path}")
        
        # Uygun başlatma scripti oluştur
        startup_script = detector.create_startup_script(project_root)
        print(f"📜 Başlatma scripti oluşturuldu: {startup_script}")
        
    except ImportError as e:
        print(f"❌ Sistem tespit modülü yüklenemedi: {e}")
        print("⚠️ Varsayılan ayarlarla devam ediliyor...")
        config = {"os_type": "unknown", "python_executable": "python"}
    
    print("\n" + "=" * 60)
    print("📋 SİSTEM DURUM RAPORU")
    print("=" * 60)
    
    # Gerekli dosyaları kontrol et
    project_root = Path(__file__).parent
    required_files = [
        ("Config dosyası", project_root / "config" / "global_config.json"),
        ("Prompt dosyası", project_root / "prompt.txt"),
        ("Veri klasörü", project_root / "veri" / "kullanicilar"),
        ("Ana script", project_root / "process_single.py"),
        ("Test script", project_root / "test_process.py")
    ]
    
    all_ready = True
    for name, path in required_files:
        exists = path.exists()
        status = "✅ MEVCUT" if exists else "❌ EKSİK"
        print(f"{name}: {status}")
        if not exists:
            all_ready = False
    
    print("\n" + "=" * 60)
    
    if all_ready:
        print("🎉 SİSTEM HAZIR! Aşağıdaki komutları kullanabilirsiniz:")
        print()
        
        # Sistem tipine göre komutlar
        if config['os_type'] == 'windows':
            print("🪟 WINDOWS KOMUTLARI:")
            print(f"  • Ana sistem çalıştır: {config['python_executable']} backend/src/main.py")
            print(f"  • Test çalıştır: {config['python_executable']} test_process.py")
            print(f"  • Tek dosya işle: {config['python_executable']} process_single.py")
            print(f"  • Gerçek fatura işle: {config['python_executable']} process_real_invoice.py <dosya_adi>")
            print("  • Başlatma scripti: Scripts\\start.bat")
        elif config['os_type'] == 'linux':
            print("🐧 LINUX KOMUTLARI:")
            print(f"  • Ana sistem çalıştır: {config['python_executable']} backend/src/main.py")
            print(f"  • Test çalıştır: {config['python_executable']} test_process.py")
            print(f"  • Tek dosya işle: {config['python_executable']} process_single.py")
            print(f"  • Gerçek fatura işle: {config['python_executable']} process_real_invoice.py <dosya_adi>")
            print("  • Başlatma scripti: ./Scripts/start.sh")
        else:
            print("🖥️ GENEL KOMUTLAR:")
            print("  • Ana sistem çalıştır: python backend/src/main.py")
            print("  • Test çalıştır: python test_process.py")
            print("  • Tek dosya işle: python process_single.py")
            print("  • Gerçek fatura işle: python process_real_invoice.py <dosya_adi>")
        
        print()
        print("📚 DOKÜMANTASYON:")
        print("  • Kurulum kılavuzu: docs/KURULUM.md")
        print("  • Windows test raporu: docs/WINDOWS_TEST_RAPORU.md")
        
    else:
        print("❌ SİSTEM HAZIR DEĞİL! Eksik dosyaları kontrol edin.")
        print("💡 Kurulum için docs/KURULUM.md dosyasını okuyun.")
    
    print("\n" + "=" * 60)
    
    # Kullanıcıya seçenek sun
    if all_ready:
        print("Ne yapmak istiyorsunuz?")
        print("1. Ana sistem çalıştır (main.py)")
        print("2. Test çalıştır")
        print("3. Sistem bilgilerini göster")
        print("4. Çıkış")

        try:
            choice = input("\nSeçiminiz (1-4): ").strip()

            if choice == "1":
                print("\n🚀 Ana sistem çalıştırılıyor...")
                os.system(f"{config['python_executable']} backend/src/main.py")
            elif choice == "2":
                print("\n🧪 Test çalıştırılıyor...")
                os.system(f"{config['python_executable']} test_process.py")
            elif choice == "3":
                if 'detector' in locals():
                    detector.print_system_info()
                else:
                    print("Sistem tespit modülü mevcut değil.")
            elif choice == "4":
                print("👋 Görüşürüz!")
            else:
                print("❌ Geçersiz seçim!")
                
        except KeyboardInterrupt:
            print("\n\n👋 Çıkış yapılıyor...")
        except Exception as e:
            print(f"\n❌ Hata: {e}")

if __name__ == "__main__":
    main()
