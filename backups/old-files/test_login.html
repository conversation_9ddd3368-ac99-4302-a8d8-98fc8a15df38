<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BILLVAT - Test Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header text-center p-4">
                        <h3 class="mb-0">
                            <i class="fas fa-robot"></i> BILLVAT AI
                        </h3>
                        <p class="mb-0">Test Login Sayfası</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <!-- Hata/Başarı Mesajları -->
                        <div id="message-container"></div>
                        
                        <!-- Test Hesapları -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Test Hesapları:</h6>
                            <strong>Admin:</strong><br>
                            Email: <EMAIL><br>
                            Şifre: admin123<br><br>
                            <strong>Test Kullanıcısı:</strong><br>
                            Email: <EMAIL><br>
                            Şifre: test123
                        </div>
                        
                        <!-- Login Form -->
                        <form id="loginForm">
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-envelope"></i> Email
                                </label>
                                <input type="email" class="form-control" name="email" value="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-lock"></i> Şifre
                                </label>
                                <input type="password" class="form-control" name="password" value="admin123" required>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Giriş Yap
                                </button>
                            </div>
                        </form>
                        
                        <!-- Test Butonları -->
                        <div class="row">
                            <div class="col-6">
                                <button class="btn btn-success w-100" onclick="testAdmin()">
                                    <i class="fas fa-crown"></i> Admin Test
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-info w-100" onclick="testUser()">
                                    <i class="fas fa-user"></i> User Test
                                </button>
                            </div>
                        </div>
                        
                        <!-- API Test -->
                        <div class="mt-4">
                            <button class="btn btn-warning w-100" onclick="testAPI()">
                                <i class="fas fa-cog"></i> API Test
                            </button>
                        </div>
                        
                        <!-- Sonuçlar -->
                        <div id="test-results" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Login Form Submit
        $('#loginForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            $.ajax({
                url: 'api.php?action=login',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    console.log('Response:', response);
                    if (response.success) {
                        showMessage('Giriş başarılı! Yönlendiriliyorsunuz...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.php';
                        }, 1500);
                    } else {
                        showMessage(response.message, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr.responseText);
                    showMessage('API Hatası: ' + error, 'danger');
                }
            });
        });
        
        function testAdmin() {
            $('input[name="email"]').val('<EMAIL>');
            $('input[name="password"]').val('admin123');
            $('#loginForm').submit();
        }
        
        function testUser() {
            $('input[name="email"]').val('<EMAIL>');
            $('input[name="password"]').val('test123');
            $('#loginForm').submit();
        }
        
        function testAPI() {
            showMessage('API Test başlatılıyor...', 'info');
            
            $.get('api.php?action=test', function(data) {
                $('#test-results').html('<div class="alert alert-success">API Çalışıyor: ' + JSON.stringify(data) + '</div>');
            }).fail(function(xhr) {
                $('#test-results').html('<div class="alert alert-danger">API Hatası: ' + xhr.responseText + '</div>');
            });
        }
        
        // Mesaj gösterme fonksiyonu
        function showMessage(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#message-container').html(alertHtml);
        }
    </script>
</body>
</html>
