{"filename": "AF1202500040_20250223_BE172585968_udr-63e2a6752c4b4fa6808bad0c8200c564.pdf", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\BILLVAT\\veri\\kullanicilar\\ornek_alanlar\\AF1202500040_20250223_BE172585968_udr-63e2a6752c4b4fa6808bad0c8200c564.pdf", "file_hash": "712ea832085d7dd5b883072b0104fef4", "status_code": 1, "status_name": "yeni_dokuman", "last_updated": "2025-06-03T17:03:46.783059", "processing_history": [{"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:40:20.272784", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:40:20.848470", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:40:47.445782", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:40:47.502000", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:52:47.967457", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:52:48.036121", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:53:30.003799", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:53:30.056772", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:58:16.844733", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T09:58:16.943062", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:01:13.917791", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:01:14.010441", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:16:15.176246", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:16:15.288312", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:20:27.509744", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:20:27.619628", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:29:11.883782", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:29:11.992456", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:40:01.110363", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:40:01.218632", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:47:28.393923", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:47:28.483761", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:48:04.880123", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:48:04.964232", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:53:03.513006", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:53:03.592534", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:57:41.383405", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T10:57:41.478232", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T12:04:54.312382", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T12:04:54.419624", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T14:44:16.329155", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T14:44:16.568860", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:09:08.426202", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:09:08.514420", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:24:03.304242", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:24:03.431940", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:27:31.039995", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:27:31.156949", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:31:16.957822", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:31:17.071860", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:44:19.307154", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:44:19.398998", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:46:49.156935", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:46:49.217124", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:54:40.370392", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:54:40.431384", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:57:38.038470", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T15:57:38.119554", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:01:16.797429", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:01:16.865364", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:04:50.910127", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:04:50.935593", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:09:45.599651", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:09:45.622195", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:16:54.017911", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:16:54.039172", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:19:41.408256", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:19:41.450882", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:22:37.713121", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:22:37.739760", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:23:40.808331", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:23:40.833758", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:42:54.813490", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:42:54.840215", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:43:58.245344", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:43:58.277458", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:47:53.602123", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:47:53.641426", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:54:02.181989", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:54:02.213989", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:58:58.079459", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T16:58:58.120997", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T17:03:46.744346", "info": "İşlem başlatıldı"}, {"status_code": 1, "status_name": "yeni_dokuman", "timestamp": "2025-06-03T17:03:46.783059", "info": "İşlem başlatıldı"}]}