#!/bin/bash

# BILLVAT Deployment Fixes Script
# <PERSON><PERSON><PERSON> yükleme ve cron job sorunlarını çözer

echo "🚀 BILLVAT Deployment Fixes Starting..."

# Renklendirme
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Container'a dosyaları kopyala
log_info "Fixing dosyalarını container'a kopyalıyor..."

# Cron setup script'ini kopyala
docker cp cron_setup.sh billvat-app:/tmp/cron_setup.sh
docker exec billvat-app chmod +x /tmp/cron_setup.sh

# Upload fix script'ini kopyala
docker cp fix_upload.php billvat-app:/var/www/html/fix_upload.php

# Cron processor'ı kopyala
docker exec billvat-app mkdir -p /var/www/html/python
docker cp python/cron_processor.py billvat-app:/var/www/html/python/cron_processor.py
docker exec billvat-app chmod +x /var/www/html/python/cron_processor.py

log_success "Dosyalar kopyalandı"

# 2. PHP extensions kurulumu
log_info "PHP extensions kuruluyor..."
docker exec billvat-app bash -c "
    apt-get update && 
    apt-get install -y libzip-dev && 
    docker-php-ext-install zip
"

if [ $? -eq 0 ]; then
    log_success "PHP extensions kuruldu"
else
    log_error "PHP extensions kurulumu başarısız"
fi

# 3. Cron job'ları ayarla
log_info "Cron job'ları ayarlanıyor..."
docker exec billvat-app /tmp/cron_setup.sh

if [ $? -eq 0 ]; then
    log_success "Cron job'ları ayarlandı"
else
    log_error "Cron job'ları ayarlanamadı"
fi

# 4. Upload fix'ini çalıştır
log_info "Upload sorunları düzeltiliyor..."
docker exec billvat-app php /var/www/html/fix_upload.php

if [ $? -eq 0 ]; then
    log_success "Upload sorunları düzeltildi"
else
    log_error "Upload düzeltmesi başarısız"
fi

# 5. Dosya izinlerini düzelt
log_info "Dosya izinleri düzeltiliyor..."
docker exec billvat-app bash -c "
    chown -R www-data:www-data /var/www/html/veri
    chmod -R 755 /var/www/html/veri
    chown -R www-data:www-data /var/www/html/logs
    chmod -R 755 /var/www/html/logs
    chown -R www-data:www-data /var/www/html/cache
    chmod -R 755 /var/www/html/cache
"

log_success "Dosya izinleri düzeltildi"

# 6. Apache'yi yeniden başlat
log_info "Apache yeniden başlatılıyor..."
docker exec billvat-app service apache2 restart

if [ $? -eq 0 ]; then
    log_success "Apache yeniden başlatıldı"
else
    log_error "Apache yeniden başlatma başarısız"
fi

# 7. Sistem durumunu kontrol et
log_info "Sistem durumu kontrol ediliyor..."
sleep 5

# Health check
HEALTH_STATUS=$(curl -s https://billvat.com/health.php -k | jq -r '.status' 2>/dev/null)
if [ "$HEALTH_STATUS" = "healthy" ]; then
    log_success "Sistem sağlıklı"
else
    log_warning "Sistem durumu: $HEALTH_STATUS"
fi

# Upload test
log_info "Upload testi yapılıyor..."
docker exec billvat-app php -r "
    echo 'Upload max filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;
    echo 'Post max size: ' . ini_get('post_max_size') . PHP_EOL;
    echo 'File uploads: ' . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . PHP_EOL;
"

# Cron test
log_info "Cron durumu kontrol ediliyor..."
docker exec billvat-app service cron status

# 8. Özet rapor
echo ""
echo "=================================="
echo "🎉 BILLVAT FIXES TAMAMLANDI!"
echo "=================================="
echo ""
echo "✅ Yapılan İşlemler:"
echo "   - PHP zip extension kuruldu"
echo "   - Cron job'ları ayarlandı"
echo "   - Dosya yükleme sorunları düzeltildi"
echo "   - Dosya izinleri düzeltildi"
echo "   - Apache yeniden başlatıldı"
echo ""
echo "🔗 Test URL'leri:"
echo "   - Ana Site: https://billvat.com"
echo "   - Health Check: https://billvat.com/health.php"
echo "   - Login: https://billvat.com/login.php"
echo ""
echo "📋 Cron Job'ları:"
echo "   - Her 5 dakikada dosya işleme"
echo "   - Her 10 dakikada sistem kontrolü"
echo "   - Günlük log temizleme"
echo "   - Haftalık sistem temizliği"
echo ""
echo "🎯 Sonraki Adımlar:"
echo "   1. Siteye giriş yapın"
echo "   2. Dosya yükleme testini yapın"
echo "   3. Cron job'ların çalıştığını kontrol edin"
echo ""

log_success "Tüm düzeltmeler tamamlandı!"
