#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import platform
import json
from pathlib import Path

class SystemDetector:
    """İşletim sistemi ve ortam tespiti için sınıf"""
    
    def __init__(self):
        self.os_type = self._detect_os()
        self.python_executable = self._get_python_executable()
        self.paths = self._get_system_paths()
        
    def _detect_os(self):
        """İşletim sistemini tespit eder"""
        system = platform.system().lower()
        
        if system == "windows":
            return "windows"
        elif system == "linux":
            return "linux"
        elif system == "darwin":
            return "macos"
        else:
            return "unknown"
    
    def _get_python_executable(self):
        """Python çalıştırılabilir dosyasını bulur"""
        if self.os_type == "windows":
            # Windows'ta py launcher'ı tercih et
            if self._command_exists("py"):
                return "py"
            elif self._command_exists("python"):
                return "python"
            elif self._command_exists("python3"):
                return "python3"
        else:
            # Linux/macOS'ta python3'ü tercih et
            if self._command_exists("python3"):
                return "python3"
            elif self._command_exists("python"):
                return "python"
        
        return "python"  # Fallback
    
    def _command_exists(self, command):
        """Komutun sistemde mevcut olup olmadığını kontrol eder"""
        try:
            if self.os_type == "windows":
                os.system(f"where {command} >nul 2>&1")
                return os.system(f"where {command} >nul 2>&1") == 0
            else:
                return os.system(f"which {command} >/dev/null 2>&1") == 0
        except:
            return False
    
    def _get_system_paths(self):
        """Sistem yollarını döndürür"""
        if self.os_type == "windows":
            return {
                "separator": "\\",
                "path_separator": ";",
                "script_extension": ".bat",
                "executable_extension": ".exe",
                "temp_dir": os.environ.get("TEMP", "C:\\temp"),
                "user_home": os.environ.get("USERPROFILE", "C:\\Users\\<USER>\n✅ Sistem konfigürasyonu kaydedildi: {saved_config}")
    
    # Başlatma scripti oluştur
    startup_script = detector.create_startup_script(project_root)
    print(f"✅ Başlatma scripti oluşturuldu: {startup_script}")
    
    # Paket yöneticisi komutları
    print(f"\n📦 Paket Yöneticisi Komutları:")
    print(f"Yükleme: {detector.get_package_manager_command('install', 'requests')}")
    print(f"Kaldırma: {detector.get_package_manager_command('uninstall', 'requests')}")
    print(f"Liste: {detector.get_package_manager_command('list')}")

if __name__ == "__main__":
    main()
