#!/usr/bin/env python3
"""
BILLVAT Cron Processor
Dosya işleme için cron job script'i
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from pathlib import Path

# Logging ayarları
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/www/html/logs/cron_processing.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class CronProcessor:
    def __init__(self):
        self.base_dir = Path('/var/www/html')
        self.veri_dir = self.base_dir / 'veri'
        self.users_dir = self.veri_dir / 'kullanicilar'
        self.processed_count = 0
        self.error_count = 0
        
    def log_status(self, message, level='info'):
        """Status mesajını logla"""
        if level == 'error':
            logger.error(message)
        elif level == 'warning':
            logger.warning(message)
        else:
            logger.info(message)
    
    def find_pending_files(self):
        """İşlenmesi gereken dosyaları bul"""
        pending_files = []
        
        if not self.users_dir.exists():
            self.log_status("Kullanıcı dizini bulunamadı", 'warning')
            return pending_files
        
        for user_dir in self.users_dir.iterdir():
            if not user_dir.is_dir():
                continue
                
            status_dir = user_dir / 'status'
            if not status_dir.exists():
                continue
            
            # Status dosyalarını kontrol et
            for status_file in status_dir.glob('*_status.json'):
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status_data = json.load(f)
                    
                    # Status 1 (yeni) veya 2 (işleniyor) olan dosyaları bul
                    if status_data.get('status', 0) in [1, 2]:
                        file_path = user_dir / status_data.get('original_name', '')
                        if file_path.exists():
                            pending_files.append({
                                'file_path': str(file_path),
                                'status_file': str(status_file),
                                'user_dir': str(user_dir),
                                'status': status_data.get('status', 0)
                            })
                            
                except Exception as e:
                    self.log_status(f"Status dosyası okuma hatası {status_file}: {e}", 'error')
        
        return pending_files
    
    def process_file(self, file_info):
        """Tek dosyayı işle"""
        try:
            file_path = file_info['file_path']
            status_file = file_info['status_file']
            
            self.log_status(f"İşleniyor: {file_path}")
            
            # Status'u işleniyor (2) olarak güncelle
            self.update_status(status_file, 2, "İşleniyor...")
            
            # OCR işlemini simüle et (gerçek implementasyon için ocr_processor.py kullanılacak)
            time.sleep(2)  # Simülasyon
            
            # Başarılı işlem için status'u 3 (işlendi) yap
            self.update_status(status_file, 3, "İşlendi")
            
            self.processed_count += 1
            self.log_status(f"Başarıyla işlendi: {file_path}")
            
        except Exception as e:
            self.error_count += 1
            self.log_status(f"İşleme hatası {file_info['file_path']}: {e}", 'error')
            
            # Hata durumunda status'u güncelle
            try:
                self.update_status(file_info['status_file'], 1, f"Hata: {str(e)}")
            except:
                pass
    
    def update_status(self, status_file, status, message=""):
        """Status dosyasını güncelle"""
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            status_data['status'] = status
            status_data['last_update'] = datetime.now().isoformat()
            if message:
                status_data['message'] = message
            
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.log_status(f"Status güncelleme hatası {status_file}: {e}", 'error')
    
    def cleanup_old_logs(self):
        """Eski log dosyalarını temizle"""
        try:
            logs_dir = self.base_dir / 'logs'
            if not logs_dir.exists():
                return
            
            # 7 günden eski log dosyalarını sil
            cutoff_time = time.time() - (7 * 24 * 60 * 60)
            
            for log_file in logs_dir.glob('*.log'):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    self.log_status(f"Eski log dosyası silindi: {log_file}")
                    
        except Exception as e:
            self.log_status(f"Log temizleme hatası: {e}", 'error')
    
    def run(self):
        """Ana işleme döngüsü"""
        start_time = time.time()
        self.log_status("=== CRON PROCESSOR BAŞLATILIYOR ===")
        
        try:
            # Bekleyen dosyaları bul
            pending_files = self.find_pending_files()
            self.log_status(f"Bekleyen dosya sayısı: {len(pending_files)}")
            
            # Dosyaları işle
            for file_info in pending_files:
                self.process_file(file_info)
            
            # Log temizliği
            self.cleanup_old_logs()
            
            # Özet
            duration = time.time() - start_time
            self.log_status(f"=== İŞLEM TAMAMLANDI ===")
            self.log_status(f"İşlenen dosya: {self.processed_count}")
            self.log_status(f"Hata sayısı: {self.error_count}")
            self.log_status(f"Süre: {duration:.2f} saniye")
            
        except Exception as e:
            self.log_status(f"Kritik hata: {e}", 'error')
            return 1
        
        return 0

if __name__ == "__main__":
    processor = CronProcessor()
    exit_code = processor.run()
    sys.exit(exit_code)
