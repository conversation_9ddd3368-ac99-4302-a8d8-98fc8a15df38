version: '3.8'

services:
  billvat-app:
    build: .
    container_name: billvat-app
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./veri:/var/www/html/veri
      - ./logs:/var/www/html/logs
      - ./config:/var/www/html/config
      - ./cache:/var/www/html/cache
    environment:
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=300
      - PHP_UPLOAD_MAX_FILESIZE=50M
      - PHP_POST_MAX_SIZE=50M
    networks:
      - billvat-network

  nginx:
    image: nginx:alpine
    container_name: billvat-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - billvat-app
    networks:
      - billvat-network

  redis:
    image: redis:alpine
    container_name: billvat-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - billvat-network

networks:
  billvat-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
