# BILLVAT Redis Connection Fix

## Problem Identified
The BILLVAT application was showing as "unhealthy" because:
1. Redis PHP extension was not installed in the container
2. Redis environment variables were not passed to the billvat-app container
3. The health.php script couldn't connect to Redis

## Changes Made

### 1. Updated docker-compose.yml
Added Redis environment variables to the `billvat-app` service:

```yaml
environment:
  # ... existing variables ...
  - REDIS_HOST=redis
  - REDIS_PORT=6379
  - REDIS_PASSWORD=${REDIS_PASSWORD:-billvat2024}
```

**Location**: Lines 31-33 in docker-compose.yml

### 2. Updated Dockerfile.minimal
Added Redis PHP extension installation:

```dockerfile
# Added these lines:
&& pecl install redis \
&& docker-php-ext-enable redis \
```

**Location**: Lines 26-27 in Dockerfile.minimal

### 3. Environment Configuration
The .env file already contains the correct Redis configuration:
- `REDIS_HOST=redis`
- `REDIS_PORT=6379`
- `REDIS_PASSWORD=billvat2024_secure`

## How the Fix Works

1. **Redis Extension**: The Dockerfile now installs the Redis PHP extension, allowing PHP to connect to Redis
2. **Environment Variables**: The docker-compose.yml now passes Redis connection details to the app container
3. **Health Check**: The health.php script can now successfully connect to Redis using the provided credentials

## Deployment Instructions

### Option 1: Use the provided script
```bash
./fix-redis-deployment.sh
```

### Option 2: Manual deployment
```bash
# Stop containers
docker-compose down

# Rebuild with Redis support
docker-compose build --no-cache billvat-app

# Start all services
docker-compose up -d

# Check health
curl http://localhost/health.php
```

## Verification Steps

1. **Check container status**:
   ```bash
   docker-compose ps
   ```

2. **Verify Redis extension is loaded**:
   ```bash
   docker exec billvat-app php -r "echo class_exists('Redis') ? 'Redis extension loaded' : 'Redis extension not found'; echo PHP_EOL;"
   ```

3. **Test Redis connection**:
   ```bash
   docker exec billvat-app php -r "$r = new Redis(); $r->connect('redis', 6379); $r->auth('billvat2024'); echo $r->ping();"
   ```

4. **Check health endpoint**:
   ```bash
   curl http://localhost/health.php | jq .services.redis
   ```

## Expected Results

After applying the fix:
- Health check should show `"redis": "ok"`
- Container status should change from "unhealthy" to "healthy"
- Redis metrics should appear in the health check response

## Troubleshooting

If issues persist:

1. **Check logs**:
   ```bash
   docker-compose logs billvat-app
   docker-compose logs billvat-redis
   ```

2. **Verify Redis is running**:
   ```bash
   docker exec billvat-redis redis-cli ping
   ```

3. **Check Redis authentication**:
   ```bash
   docker exec billvat-redis redis-cli -a billvat2024 ping
   ```

4. **Verify environment variables in container**:
   ```bash
   docker exec billvat-app env | grep REDIS
   ```

## Files Modified
- `docker-compose.yml` - Added Redis environment variables
- `Dockerfile.minimal` - Added Redis PHP extension installation
- `fix-redis-deployment.sh` - Created deployment script (new file)
- `REDIS_FIX_SUMMARY.md` - This documentation (new file)

## Notes
- The Redis password in .env is `billvat2024_secure` but the fallback in docker-compose.yml is `billvat2024`
- The environment variable `REDIS_PASSWORD` from .env will take precedence
- All containers need to be rebuilt for the Dockerfile changes to take effect
