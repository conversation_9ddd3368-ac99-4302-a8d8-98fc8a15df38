#!/bin/bash

# BILLVAT Redis Fix Deployment Script
# This script fixes the Redis connection issue by rebuilding the containers with proper configuration

echo "🔧 BILLVAT Redis Fix Deployment Script"
echo "======================================"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: docker-compose.yml not found. Please run this script from the project root directory."
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running. Please start Docker first."
    exit 1
fi

echo "📋 Changes made to fix Redis connection:"
echo "1. Added Redis environment variables to billvat-app service in docker-compose.yml"
echo "2. Added Redis PHP extension installation to Dockerfile.minimal"
echo "3. Environment variables added:"
echo "   - REDIS_HOST=redis"
echo "   - REDIS_PORT=6379"
echo "   - REDIS_PASSWORD=\${REDIS_PASSWORD:-billvat2024}"
echo ""

# Ask for confirmation
read -p "🚀 Do you want to proceed with the deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled."
    exit 1
fi

echo "🛑 Stopping existing containers..."
docker-compose down

echo "🏗️  Rebuilding billvat-app container with Redis support..."
docker-compose build --no-cache billvat-app

echo "🚀 Starting all services..."
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 30

echo "🔍 Checking container status..."
docker-compose ps

echo "🏥 Testing health check..."
sleep 10
curl -s http://localhost/health.php | python3 -m json.tool || echo "Health check endpoint not responding yet"

echo ""
echo "✅ Deployment completed!"
echo ""
echo "📊 Next steps:"
echo "1. Check if all containers are running: docker-compose ps"
echo "2. Check health status: curl http://localhost/health.php"
echo "3. Check logs if needed: docker-compose logs billvat-app"
echo "4. Check Redis connection: docker exec billvat-app php -r \"echo class_exists('Redis') ? 'Redis extension loaded' : 'Redis extension not found'; echo PHP_EOL;\""
echo ""
echo "🔧 If issues persist:"
echo "1. Check container logs: docker-compose logs"
echo "2. Verify Redis is running: docker exec billvat-redis redis-cli ping"
echo "3. Test Redis connection from app: docker exec billvat-app php -r \"\$r = new Redis(); \$r->connect('redis', 6379); \$r->auth('billvat2024'); echo \$r->ping();\""
