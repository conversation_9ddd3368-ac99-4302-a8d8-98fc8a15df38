<?php
/**
 * BILLVAT Health Check Endpoint
 * Comprehensive system health monitoring
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Başlangıç zamanı
$start_time = microtime(true);

$health = [
    'status' => 'healthy',
    'timestamp' => date('c'),
    'version' => '1.0.0',
    'environment' => $_ENV['APP_ENV'] ?? 'production',
    'domain' => $_ENV['DOMAIN_NAME'] ?? 'billvat.com',
    'services' => [],
    'metrics' => [],
    'checks' => []
];

// Sistem bilgileri
$health['system'] = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
    'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . ' MB',
    'uptime' => file_exists('/proc/uptime') ? trim(file_get_contents('/proc/uptime')) : 'Unknown'
];

// Storage kontrolü
$storage_config = '/var/www/html/config/storage.json';
if (file_exists($storage_config)) {
    $health['services']['storage_config'] = 'ok';
    
    // Storage config'i oku
    $config = json_decode(file_get_contents($storage_config), true);
    $health['storage_type'] = $config['storage_type'] ?? 'unknown';
} else {
    $health['services']['storage_config'] = 'error';
    $health['status'] = 'unhealthy';
    $health['checks'][] = 'Storage configuration file missing';
}

// Veri dizini kontrolü
$data_dir = '/var/www/html/veri';
if (is_dir($data_dir) && is_writable($data_dir)) {
    $health['services']['data_directory'] = 'ok';
    
    // Disk kullanımı
    $disk_free = disk_free_space($data_dir);
    $disk_total = disk_total_space($data_dir);
    $disk_used_percent = round((($disk_total - $disk_free) / $disk_total) * 100, 2);
    
    $health['metrics']['disk_usage'] = [
        'free' => formatBytes($disk_free),
        'total' => formatBytes($disk_total),
        'used_percent' => $disk_used_percent
    ];
    
    if ($disk_used_percent > 90) {
        $health['status'] = 'warning';
        $health['checks'][] = 'Disk usage is high: ' . $disk_used_percent . '%';
    }
} else {
    $health['services']['data_directory'] = 'error';
    $health['status'] = 'unhealthy';
    $health['checks'][] = 'Data directory is not writable';
}

// Redis kontrolü
if (class_exists('Redis')) {
    try {
        $redis = new Redis();
        $redis_host = $_ENV['REDIS_HOST'] ?? 'redis';
        $redis_port = $_ENV['REDIS_PORT'] ?? 6379;
        $redis_password = $_ENV['REDIS_PASSWORD'] ?? null;
        
        $redis->connect($redis_host, $redis_port, 2); // 2 second timeout
        
        if ($redis_password) {
            $redis->auth($redis_password);
        }
        
        $ping_result = $redis->ping();
        if ($ping_result === '+PONG' || $ping_result === 'PONG' || $ping_result === true) {
            $health['services']['redis'] = 'ok';
            
            // Redis info
            $redis_info = $redis->info();
            $health['metrics']['redis'] = [
                'version' => $redis_info['redis_version'] ?? 'unknown',
                'connected_clients' => $redis_info['connected_clients'] ?? 0,
                'used_memory' => formatBytes($redis_info['used_memory'] ?? 0),
                'uptime_in_seconds' => $redis_info['uptime_in_seconds'] ?? 0
            ];
        } else {
            $health['services']['redis'] = 'error';
            $health['checks'][] = 'Redis ping failed';
        }
        
        $redis->close();
    } catch (Exception $e) {
        $health['services']['redis'] = 'error';
        $health['checks'][] = 'Redis connection failed: ' . $e->getMessage();
    }
} else {
    $health['services']['redis'] = 'not_available';
    $health['checks'][] = 'Redis extension not installed';
}

// Database kontrolü (gelecekte)
$health['services']['database'] = 'not_configured';

// SSL sertifikası kontrolü
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    $health['ssl'] = [
        'enabled' => true,
        'protocol' => $_SERVER['SSL_PROTOCOL'] ?? 'unknown',
        'cipher' => $_SERVER['SSL_CIPHER'] ?? 'unknown'
    ];
    
    // Sertifika süresi kontrolü
    $cert_file = '/etc/letsencrypt/live/billvat.com/fullchain.pem';
    if (file_exists($cert_file)) {
        $cert_data = openssl_x509_parse(file_get_contents($cert_file));
        if ($cert_data) {
            $valid_until = $cert_data['validTo_time_t'];
            $days_until_expiry = floor(($valid_until - time()) / 86400);
            
            $health['ssl']['expires_in_days'] = $days_until_expiry;
            
            if ($days_until_expiry < 30) {
                $health['status'] = 'warning';
                $health['checks'][] = "SSL certificate expires in $days_until_expiry days";
            }
        }
    }
} else {
    $health['ssl'] = ['enabled' => false];
}

// Dosya izinleri kontrolü
$critical_dirs = [
    '/var/www/html/veri' => 'writable',
    '/var/www/html/logs' => 'writable',
    '/var/www/html/config' => 'writable',
    '/var/www/html/cache' => 'writable'
];

$permissions_ok = true;
foreach ($critical_dirs as $dir => $required) {
    if (!is_dir($dir)) {
        $health['checks'][] = "Directory missing: $dir";
        $permissions_ok = false;
    } elseif ($required === 'writable' && !is_writable($dir)) {
        $health['checks'][] = "Directory not writable: $dir";
        $permissions_ok = false;
    }
}

$health['services']['file_permissions'] = $permissions_ok ? 'ok' : 'error';
if (!$permissions_ok && $health['status'] === 'healthy') {
    $health['status'] = 'unhealthy';
}

// PHP extensions kontrolü
$required_extensions = ['json', 'curl', 'mbstring', 'zip'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    $health['services']['php_extensions'] = 'error';
    $health['checks'][] = 'Missing PHP extensions: ' . implode(', ', $missing_extensions);
    $health['status'] = 'unhealthy';
} else {
    $health['services']['php_extensions'] = 'ok';
}

// Yanıt süresi
$response_time = round((microtime(true) - $start_time) * 1000, 2);
$health['metrics']['response_time_ms'] = $response_time;

// Genel durum kontrolü
if ($health['status'] === 'healthy' && !empty($health['checks'])) {
    $health['status'] = 'warning';
}

// HTTP status code
$status_code = 200;
if ($health['status'] === 'unhealthy') {
    $status_code = 503;
} elseif ($health['status'] === 'warning') {
    $status_code = 200; // Warning'ler için 200 döndür
}

// Yardımcı fonksiyon
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

http_response_code($status_code);
echo json_encode($health, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
?>
