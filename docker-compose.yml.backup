version: '3.8'

services:
  billvat-app:
    build:
      context: .
      dockerfile: Dockerfile.minimal
    container_name: billvat-app
    volumes:
      - ./veri:/var/www/html/veri
      - ./logs:/var/www/html/logs
      - ./config:/var/www/html/config
      - ./database:/var/www/html/database
      - ./cache:/var/www/html/cache
      - billvat-db:/var/www/html/database/sqlite
    environment:
      - PHP_ENV=production
      - STORAGE_TYPE=${STORAGE_TYPE:-local}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
      - S3_BUCKET=${S3_BUCKET:-}
      - S3_ENDPOINT=${S3_ENDPOINT:-}
      - WASABI_ACCESS_KEY=${WASABI_ACCESS_KEY:-}
      - WASABI_SECRET_KEY=${WASABI_SECRET_KEY:-}
      - WASABI_BUCKET=${WASABI_BUCKET:-}
      - WASABI_REGION=${WASABI_REGION:-us-east-1}
      - DOMAIN_NAME=${DOMAIN_NAME:-billvat.com}
      - APP_ENV=production
      - APP_DEBUG=false
    restart: unless-stopped
    networks:
      - billvat-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health.php"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: billvat-redis
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-billvat2024}
    restart: unless-stopped
    networks:
      - billvat-network

  nginx:
    image: nginx:alpine
    container_name: billvat-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx-prod.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
      - certbot_conf:/etc/letsencrypt
      - certbot_www:/var/www/certbot
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - billvat-app
    restart: unless-stopped
    networks:
      - billvat-network
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME:-billvat.com}
    command: "/bin/sh -c 'while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g \"daemon off;\"'"

  certbot:
    image: certbot/certbot
    container_name: billvat-certbot
    volumes:
      - certbot_conf:/etc/letsencrypt
      - certbot_www:/var/www/certbot
      - ./logs/certbot:/var/log/letsencrypt
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    restart: unless-stopped
    networks:
      - billvat-network

volumes:
  redis_data:
    driver: local
  billvat-db:
    driver: local
  certbot_conf:
    driver: local
  certbot_www:
    driver: local

networks:
  billvat-network:
    driver: bridge
