#!/bin/bash

# BILLVAT Cron Job Setup Script
# Docker container içinde cron job'ları ayarlar

echo "🔧 BILLVAT Cron Job Setup Starting..."

# Cron paketini yükle
apt-get update && apt-get install -y cron

# Cron servisini başlat
service cron start

# Cron job'ları tanımla
cat > /tmp/billvat_cron << 'EOF'
# BILLVAT Cron Jobs
# Her 5 dakikada bir dosya işleme kontrolü
*/5 * * * * cd /var/www/html && php -f python/ocr_processor.py >> /var/www/html/logs/cron_processing.log 2>&1

# Her 10 dakikada bir sistem durumu kontrolü
*/10 * * * * cd /var/www/html && php -f health.php > /var/www/html/logs/health_check.log 2>&1

# Her gece 02:00'da log temizleme
0 2 * * * find /var/www/html/logs -name "*.log" -mtime +7 -delete

# Her gece 03:00'da veritabanı yedekleme
0 3 * * * cd /var/www/html && php -f backend/backup_database.php >> /var/www/html/logs/backup.log 2>&1

# Her hafta Pazar günü 04:00'da sistem temizliği
0 4 * * 0 cd /var/www/html && php -f backend/system_cleanup.php >> /var/www/html/logs/cleanup.log 2>&1
EOF

# Cron job'ları yükle
crontab /tmp/billvat_cron

# Cron job'ları kontrol et
echo "📋 Yüklenen Cron Job'ları:"
crontab -l

# Cron servisini yeniden başlat
service cron restart

# Cron durumunu kontrol et
service cron status

echo "✅ Cron job'ları başarıyla ayarlandı!"

# Log dizinlerini oluştur
mkdir -p /var/www/html/logs
chown -R www-data:www-data /var/www/html/logs
chmod -R 755 /var/www/html/logs

echo "📁 Log dizinleri hazırlandı!"
