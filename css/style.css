/* BILLVAT Custom Styles */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0 !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: #495057;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    border-left: 4px solid;
}

.alert-primary {
    border-left-color: var(--primary-color);
}

.alert-success {
    border-left-color: var(--success-color);
}

.alert-danger {
    border-left-color: var(--danger-color);
}

.alert-warning {
    border-left-color: var(--warning-color);
}

.alert-info {
    border-left-color: var(--info-color);
}

/* Modals */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

/* File Upload Area */
.upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(102, 126, 234, 0.05);
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(102, 126, 234, 0.1);
}

/* File Items */
.file-item {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: #e9ecef;
    border-color: var(--primary-color);
}

/* Sidebar */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-new { background-color: #6c757d; }
.status-processing { background-color: #ffc107; }
.status-processed { background-color: #17a2b8; }
.status-transferred { background-color: #28a745; }
.status-archived { background-color: #343a40; }
.status-error { background-color: #dc3545; }

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .upload-area {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .card {
        border-radius: 10px;
    }
    
    .modal-content {
        border-radius: 10px;
    }
    
    .btn {
        padding: 0.5rem 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #212529;
        --dark-color: #f8f9fa;
    }
    
    body {
        background-color: #1a1a1a;
        color: #f8f9fa;
    }
    
    .card {
        background-color: #2d3748;
        color: #f8f9fa;
    }
    
    .table {
        color: #f8f9fa;
    }
    
    .form-control, .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #f8f9fa;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    body {
        background: white;
        color: black;
    }
}
